# 组件拆分设计文档

## 组件架构概览

### 当前问题
- `FinancialSystemComponent` 超过800行代码，职责过重
- 业务逻辑与UI逻辑混合
- 难以维护和测试
- 组件复用性差

### 目标架构
```
FinancialApp (根应用)
├── AppHeader (应用头部)
├── NavigationTabs (导航标签)
├── BuInfoSection (BU信息区域)
│   ├── BasicInfoForm (基础信息表单)
│   ├── ContractInfoForm (合同信息表单)
│   └── BusinessConfigForm (业务配置表单)
├── LeaseSection (租赁区域)
│   ├── LeaseListTable (租赁列表表格)
│   └── LeaseDetailModal (租赁详情弹窗)
│       ├── LeaseInfoTab (租赁信息标签)
│       ├── RbTab (固定租金标签)
│       └── SovTab (抽成租金标签)
└── ValidationPanel (验证面板)
```

## 核心组件设计

### 1. FinancialApp (根组件)

**职责**:
- 应用状态管理
- 路由控制
- 全局事件处理

**接口设计**:
```typescript
interface FinancialAppProps {
  initialData?: FinancialData;
  mode?: 'view' | 'edit';
  onSave?: (data: FinancialData) => Promise<void>;
  onValidate?: (result: ValidationResult) => void;
}

interface FinancialAppState {
  currentView: 'bu' | 'lease';
  isLoading: boolean;
  hasUnsavedChanges: boolean;
}
```

**实现要点**:
- 使用状态管理库（Zustand/Pinia）
- 实现数据持久化
- 处理全局错误和加载状态

### 2. BuInfoSection (BU信息区域)

**职责**:
- 管理BU基本信息
- 协调子表单组件
- 处理BU级别的验证

**组件结构**:
```typescript
interface BuInfoSectionProps {
  data: Finance;
  readonly?: boolean;
  onUpdate: (data: Partial<Finance>) => void;
  onValidate: (result: ValidationResult) => void;
}

// 子组件
const BuInfoSection = () => {
  return (
    <div className="bu-info-section">
      <NavigationTabs tabs={buTabs} />
      <div className="tab-content">
        <BasicInfoForm />
        <ContractInfoForm />
        <BusinessConfigForm />
      </div>
    </div>
  );
};
```

### 3. BasicInfoForm (基础信息表单)

**职责**:
- 餐厅基础信息录入
- 实时验证
- 字段联动

**接口设计**:
```typescript
interface BasicInfoFormProps {
  data: {
    buCode: string;
    buName: string;
    finMarket: string;
    bizType: string;
  };
  errors?: ValidationError[];
  warnings?: ValidationWarning[];
  onUpdate: (field: string, value: any) => void;
  onBlur: (field: string) => void;
}
```

**验证规则**:
```typescript
const basicInfoValidationRules = {
  buCode: [
    { required: true, message: '餐厅编号不能为空' },
    { pattern: /^\d+$/, message: '餐厅编号必须为数字' }
  ],
  buName: [
    { required: true, message: '餐厅名称不能为空' },
    { maxLength: 50, message: '餐厅名称不能超过50个字符' }
  ],
  finMarket: [
    { required: true, message: '财务市场不能为空' }
  ]
};
```

### 4. LeaseSection (租赁区域)

**职责**:
- 租赁列表展示
- 租赁详情管理
- 租赁操作协调

**组件结构**:
```typescript
interface LeaseSectionProps {
  leaseDetails: LeaseDetail[];
  currentLease?: LeaseDetail;
  onAddLease: () => void;
  onEditLease: (index: number) => void;
  onDeleteLease: (index: number) => void;
  onSaveLease: (lease: LeaseDetail) => Promise<void>;
}

const LeaseSection = () => {
  const [showModal, setShowModal] = useState(false);
  const [currentLeaseIndex, setCurrentLeaseIndex] = useState(-1);
  
  return (
    <div className="lease-section">
      <LeaseListTable 
        data={leaseDetails}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onAdd={handleAdd}
      />
      
      {showModal && (
        <LeaseDetailModal
          lease={currentLease}
          onSave={handleSave}
          onClose={() => setShowModal(false)}
        />
      )}
    </div>
  );
};
```

### 5. LeaseListTable (租赁列表表格)

**职责**:
- 租赁记录展示
- 操作按钮管理
- 状态指示

**功能特性**:
- 虚拟滚动（大数据量）
- 排序和筛选
- 批量操作
- 状态指示（有效/无效/警告）

**实现要点**:
```typescript
interface LeaseListTableProps {
  data: LeaseDetail[];
  loading?: boolean;
  onEdit: (index: number) => void;
  onDelete: (index: number) => void;
  onAdd: () => void;
  onSort?: (field: string, direction: 'asc' | 'desc') => void;
}

// 虚拟滚动实现
const VirtualizedTable = ({ data, itemHeight = 50 }) => {
  const [scrollTop, setScrollTop] = useState(0);
  const containerHeight = 400;
  
  const visibleItems = useMemo(() => {
    const startIndex = Math.floor(scrollTop / itemHeight);
    const endIndex = Math.min(
      startIndex + Math.ceil(containerHeight / itemHeight) + 5,
      data.length
    );
    
    return data.slice(startIndex, endIndex).map((item, index) => ({
      ...item,
      index: startIndex + index
    }));
  }, [data, scrollTop, itemHeight]);
  
  return (
    <div 
      className="virtual-table"
      style={{ height: containerHeight }}
      onScroll={(e) => setScrollTop(e.target.scrollTop)}
    >
      {visibleItems.map(item => (
        <TableRow key={item.index} data={item} />
      ))}
    </div>
  );
};
```

### 6. LeaseDetailModal (租赁详情弹窗)

**职责**:
- 租赁详情编辑
- 标签页管理
- 数据保存

**组件结构**:
```typescript
interface LeaseDetailModalProps {
  lease: LeaseDetail;
  readonly?: boolean;
  onSave: (lease: LeaseDetail) => Promise<void>;
  onClose: () => void;
}

const LeaseDetailModal = ({ lease, onSave, onClose }) => {
  const [activeTab, setActiveTab] = useState('lease');
  const [localLease, setLocalLease] = useState(lease);
  const [hasChanges, setHasChanges] = useState(false);
  
  const handleSave = async () => {
    try {
      await onSave(localLease);
      setHasChanges(false);
      onClose();
    } catch (error) {
      // 错误处理
    }
  };
  
  return (
    <Modal size="large" onClose={onClose}>
      <ModalHeader>
        <h3>租赁详情 - LeaseNo.{lease.lease.leaseCode}</h3>
        <div className="lease-info">
          <span>餐厅：{buCode}</span>
          <span>用途：{getUseOfLeaseNoText(lease.lease.useOfLeaseNo)}</span>
        </div>
      </ModalHeader>
      
      <ModalBody>
        <TabContainer activeTab={activeTab} onTabChange={setActiveTab}>
          <Tab id="lease" title="Lease">
            <LeaseInfoTab 
              data={localLease.lease}
              onChange={(data) => updateLease('lease', data)}
            />
          </Tab>
          <Tab id="rb" title="RB">
            <RbTab 
              data={localLease.rb}
              onChange={(data) => updateLease('rb', data)}
            />
          </Tab>
          <Tab id="sov" title="SOV">
            <SovTab 
              data={localLease.sov}
              onChange={(data) => updateLease('sov', data)}
            />
          </Tab>
        </TabContainer>
      </ModalBody>
      
      <ModalFooter>
        <Button variant="secondary" onClick={onClose}>
          取消
        </Button>
        <Button 
          variant="primary" 
          onClick={handleSave}
          disabled={!hasChanges}
        >
          保存
        </Button>
      </ModalFooter>
    </Modal>
  );
};
```

### 7. RbTab (固定租金标签)

**职责**:
- 固定租金配置
- Phase管理
- 金额计算

**核心功能**:
```typescript
interface RbTabProps {
  data: RecurringBilling;
  onChange: (data: RecurringBilling) => void;
  readonly?: boolean;
}

const RbTab = ({ data, onChange }) => {
  const [phases, setPhases] = useState(data.phases);
  
  const addPhase = () => {
    const newPhase = createDefaultRbPhase();
    setPhases([...phases, newPhase]);
    onChange({ ...data, phases: [...phases, newPhase] });
  };
  
  const updatePhase = (index: number, phaseData: Partial<RbPhase>) => {
    const updatedPhases = phases.map((phase, i) => 
      i === index ? { ...phase, ...phaseData } : phase
    );
    setPhases(updatedPhases);
    onChange({ ...data, phases: updatedPhases });
  };
  
  const deletePhase = (index: number) => {
    const updatedPhases = phases.filter((_, i) => i !== index);
    setPhases(updatedPhases);
    onChange({ ...data, phases: updatedPhases });
  };
  
  return (
    <div className="rb-tab">
      <RbBasicConfig 
        data={data}
        onChange={onChange}
      />
      
      <PhaseManager
        phases={phases}
        onAdd={addPhase}
        onUpdate={updatePhase}
        onDelete={deletePhase}
      />
    </div>
  );
};
```

### 8. SovTab (抽成租金标签)

**职责**:
- 抽成租金配置
- SOV规则管理
- 计算方法设置

**实现要点**:
```typescript
interface SovTabProps {
  data: SalesOverage;
  onChange: (data: SalesOverage) => void;
  readonly?: boolean;
}

const SovTab = ({ data, onChange }) => {
  const [sovRules, setSovRules] = useState(data.sovRules);
  
  const addSovRule = () => {
    const newRule = createDefaultSovRule();
    setSovRules([...sovRules, newRule]);
    onChange({ ...data, sovRules: [...sovRules, newRule] });
  };
  
  return (
    <div className="sov-tab">
      <SovBasicConfig 
        data={data}
        onChange={onChange}
      />
      
      <SovRuleManager
        rules={sovRules}
        onAdd={addSovRule}
        onUpdate={updateSovRule}
        onDelete={deleteSovRule}
      />
    </div>
  );
};
```

## 通用组件设计

### 1. FormField (表单字段)

**职责**:
- 统一的表单字段样式
- 验证状态显示
- 错误信息展示

```typescript
interface FormFieldProps {
  label: string;
  required?: boolean;
  error?: string;
  warning?: string;
  children: React.ReactNode;
  helpText?: string;
}

const FormField = ({ label, required, error, warning, children, helpText }) => {
  return (
    <div className={`form-field ${error ? 'error' : ''} ${warning ? 'warning' : ''}`}>
      <label className="form-label">
        {required && <span className="required">*</span>}
        {label}
        {error && <i className="error-icon" />}
        {warning && <i className="warning-icon" />}
      </label>
      {children}
      {error && <div className="error-message">{error}</div>}
      {warning && <div className="warning-message">{warning}</div>}
      {helpText && <div className="help-text">{helpText}</div>}
    </div>
  );
};
```

### 2. DataTable (数据表格)

**职责**:
- 通用数据表格
- 排序、筛选、分页
- 虚拟滚动支持

### 3. Modal (模态框)

**职责**:
- 通用模态框组件
- 尺寸和位置控制
- 键盘和点击外部关闭

## 组件通信设计

### 1. 状态管理
使用Zustand进行全局状态管理：

```typescript
interface FinancialStore {
  // 状态
  buInfo: Finance;
  leaseDetails: LeaseDetail[];
  currentLeaseIndex: number;
  validationResult: ValidationResult;
  
  // 操作
  updateBuInfo: (data: Partial<Finance>) => void;
  addLeaseDetail: (detail: LeaseDetail) => void;
  updateLeaseDetail: (index: number, detail: Partial<LeaseDetail>) => void;
  deleteLeaseDetail: (index: number) => void;
  setCurrentLease: (index: number) => void;
  validateAll: () => ValidationResult;
}
```

### 2. 事件系统
使用EventEmitter进行组件间通信：

```typescript
// 事件类型定义
interface FinancialEvents {
  'lease:add': LeaseDetail;
  'lease:update': { index: number; detail: LeaseDetail };
  'lease:delete': number;
  'validation:error': ValidationError[];
  'data:save': FinancialData;
}

// 事件管理器
class EventManager extends EventEmitter3<FinancialEvents> {
  // 事件处理逻辑
}
```

## 迁移策略

### 阶段1：基础组件开发
1. 开发通用组件（FormField, Modal, DataTable等）
2. 建立组件库和样式系统
3. 创建组件文档和示例

### 阶段2：业务组件拆分
1. 拆分BuInfoSection及其子组件
2. 开发LeaseSection和相关组件
3. 实现状态管理和数据流

### 阶段3：功能迁移
1. 迁移现有业务逻辑
2. 实现验证系统
3. 优化性能和用户体验

### 阶段4：测试和优化
1. 编写组件测试
2. 性能测试和优化
3. 用户体验测试
