export interface FinancialData {
    finance: Finance;
}

export interface Finance {
    objectId:string;//唯一标识
    buCode?: string;//餐厅编号
    buName?: string;//餐厅名称
    finMarket?: string;//市场
    contractType?: string;//合同类型
    strategicAlliance?: string;//策略联盟业主
    bizType?: string;//业务类别
    partyA?: string;//甲方
    addOfPartyA?: string;//甲方地址
    partyB?: string;//合同乙方（我司市场法人）
    addOfPartyB?: string;//乙方地址
    addOfStore?: string;//租赁店铺地址
    signDate?: string;//签约日期 YYYY-MM-dd
    openDate?: string;//开业日期 YYYY-MM-dd
    estimateOpen?: string;//下拉框 是否是预计开业日期？ 取值：预计、空 显示：是、否
    returnAreaYes?: string;//租赁期满后是否恢复原状
    noSublet?: string;//是否禁止转租
    areaSM?: string;//计租面积(SM)
    areaRemark?: string;//分楼层面积
    certEffectiveDate?: string;//房产证生效日
    certExpiryDate?: string;//房产证到期日
    leaseTypePlanning?: string;//抽成租金为单一比例or多比例?
    leaseTypeDE?: string;//租金类型
    leaseTypeRE?: string;//RE/复核用
    depositAmount?: string;//定金金额（元）
    idcAmount?: string;//初始直接费用金额(元)
    leaseIncentives?: string;//补偿金金额（元）
    transferSubject?: string;//是否转入中转科目
    reNotes?: string;//RE Notes
    bondAmount?: string;//保证金金额（元）
    bondReturnDate?: string;//返还时间
    guaranteeAmount?: string;//保函金额（元）
    leaseDetails?:LeaseDetail[];
    changeContType?: string;//变更合同类型
    anchor?:Finance
}

export interface LeaseDetail {
    objectId?: string;//唯一编号
    lease?: Lease;
    rb?: RecurringBilling;
    sov?:SalesOverage;
    anchor?:LeaseDetail
}

export interface RecurringBilling {
    objectId:string;//唯一标识
    rbPaymentFrequency?: string;//RB支付频率
    rbPaymentType?: string;//RB支付类型
    paymentRent?: string;//付款期间与租金所属期间关系
    dba?: string;//DBA
    paymentEarlyRentYesDay?: string;//提前/晚于月份数
    paymentLease?: string;//付款方式
    rbFeeDesc?: string;//其他费用具体描述
    rbAnnualSettleYes?:string;//是否年度结算
    rentAnnual?:string;//租赁年度定义
    rbtrRent?:string;//固定租金税率（%）
    rbtrMgmt?:string;//物管费税率（%）
    rbtrAdv?:string;//"广告费税率（%）
    rbtrFee?:string;//其他费用税率（%）
    relation?:string;//关联要素
    phases?:RbPhase[]
    anchor?:RecurringBilling
}

export interface RbPhase {
    objectId: string;
    rbId: string;
    rbRateNum: number;
    rbBeginDate: string;
    rbEndDate: string;
    rbAccruedAmountRent: string;//固定租金（元）
    rbAccruedAmountMgmt: string;//物业费计提
    rbAccruedAmountAdv: string;//广告费计提
    rbAccruedAmountFee: string;//其他费计提
    rbPaymentAmountRent: string;//固定租金付款
    rbPaymentAmountMgmt: string;//物业费付款
    rbPaymentAmountAdv: string;//广告费付款
    rbPaymentAmountFee: string;//其他费付款
    anchor?: RbPhase;
}

export interface SalesOverage {
    objectId:string;//唯一标识
    sovBreakMonthYes?: string;//结算是否破月?
    sovLeaseVAT?: string;//增值税税率%
    calculationMethod?: string;//多档抽成计算方法
    sovPaymentType?:string;//付款类型
    minMaxNeedYes?:string;//是否需要计算固定与抽成孰高
    minMaxType1?:string;//保底租金（和RENP取高）
    minMaxType2?:string;//保底物管费（和MGMA取高）抽成租金（和RENP取高）
    sovRules?:SovRule[];
    anchor?:SalesOverage;
}

export interface SovRule {
    objectId:string;//唯一标识
    sovId?:string;//
    sovIndexNo?:number;//
    sovAnnualSettleYes?:string;//是否需要结算
    annualSettleFrequency?:string;//结算频率
    annualSettleMonth?:string;//结算月份
    sovBeginDate?:string;//抽成开始日期 格式 YY-MM-DD
    sovEndDate?:string;//抽成结束日期 格式 YY-MM-DD
    paymentOnSovYes?:string;//是否要基于抽成付款
    sovRuleList?:SovRuleList[];
    anchor?:SovRule;
}

export interface SovRuleList {
    objectId?:string;//唯一标识
    sovRuleId?:string;//
    rateNum?:number;//
    breakpointYes?:string;
    breakpoint?:string;//突破点金额(元)
    sovPayPercentage?:string;//付款抽成比例(%)
    sovAccPercentage?:string;//计提抽成比例(%)
    anchor?:SovRuleList;
}

export interface Lease {
    objectId?:string;//唯一标识
    leaseCode?: string;//LeaseNo
    useOfLeaseNo?: string;//LeaseNo.用途
    rentName?: string;//租金名称
    rentType?: string;//租赁类型
    landlordCode?: string;//业主JDE号
    landlordName?: string;//业主名称
    receivedCode?: string;//实际收款人JDE号
    receivedName?: string;//实际收款人名称
    taxplayerJde?: string;//开票人JDE号(三流不合一时需要填写)
    taxplayerName?: string;//开票人名称(三流不合一时需要填写)
    mergeOthersYes?: string;//是否与同组内其他lease合并计算
    leaseGroup?: string;//租赁组别
    strategyAlianceYes?: string;//是否独立第三方物管费
    rentAreaYes?: string;//是否归还面积
    yearlyNote?: string;//年度通知单
    receiverNote?: string;//通知单是否发给收款人
    monthlyNote?: string;//月度通知单
    unit?: string;//单元号
    beginDate?: string;//房产交付日期
    freeBeginDate?: string;//免租开始日期
    freeEndDate?: string;//免租结束日期
    startRentDate?: string;//起算日期
    orgEndDate?: string;//租赁期限结束日期
    salesCalculation?: string;//营业额取数规则
    relation?: string;//关联要素
    anchor?:Lease
    [key: string]: any; // This allows any string to be used as an index
}

export interface ComputeConfig {
    sources: string[];
    onlyOnUserInput:boolean;
    calculate: string | ((values: any[]) => any);  // 可以是函数名字符串或函数
}

export interface ValidationRule {
    required?: boolean;
    regex?: string | RegExp;
    warning?: string;
    warningMessage?: string;
    min?: number;
    max?: number;
    alias?: 'currency' | 'numeric';
}

export interface ValidationErrorDetail {
    path: string;        // 字段路径，例如 "finance.buCode" 或 "leaseDetails[0].lease.leaseCode"
    message: string;     // 错误原因，例如 "必填字段为空"
    element?: Element;   // 对应的 DOM 元素，用于定位和添加错误样式
}

export interface ValidationResult {
    isValid: boolean;                     // 整体验证结果
    buErrors: ValidationErrorDetail[];    // BU 视图的错误
    leaseErrors: Array<{                  // LeaseDetail 视图的错误
        index: number;                    // LeaseDetail 的索引
        errors: ValidationErrorDetail[];  // 该 LeaseDetail 的错误详情
    }>;
    buWarnings: ValidationWarningDetail[];// BU 视图的警告
    leaseWarnings: Array<{                // LeaseDetail 视图的警告
        index: number;                    // LeaseDetail 的索引
        warnings: ValidationWarningDetail[]; // 该 LeaseDetail 的警告详情
    }>;
}

export interface ValidationWarningDetail {
    path: string;        // 字段路径
    message: string;     // 警告消息
    element?: Element;   // 对应的 DOM 元素
}

export interface ValidationError {
    path: string;
    message: string;
}

export interface ValidationWarning {
    path: string;
    message: string;
}

export interface ValidationErrorDetail {
    path: string;        // 字段路径，例如 "lease.leaseCode" 或 "rb.phases[0].rbBeginDate"
    message: string;     // 错误原因，例如 "必填字段为空" 或 "开始日期晚于结束日期"
}
export interface ValidationValueResult {
    isValid: boolean;
    warningMessage?: string;
    errorMessage?: string; //
}

export interface LeaseDetailValidationResult {
    invalidDetails: number[];                          // 无效的 LeaseDetail 索引
    errors: Array<{ index: number; details: ValidationErrorDetail[] }>; // 每个无效 LeaseDetail 的错误详情
    warnings: Array<{ index: number; message: string[] }>;              // 警告信息
}

export type FormElement = HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement | HTMLButtonElement;

export interface DatepickerEventObject {
    date: Date;          // 选择的日期
    format(format: string): string;  // 格式化日期的方法
    type: string;        // 事件类型
}

export interface AlpineComponent {
    data: FinancialData;
    indexLeaseDetail: number;
    currentLeaseDetail: LeaseDetail;
    isBuVisible: boolean;
    isLeaseVisible: boolean;
    invalidDetails: number[];
    warningDetails: Array<{ index: number; message: string[] }>;
    showBuView: () => void;
    showLeaseView: () => void;
}