import { FinancialData, LeaseDetail, RbPhase, SovRule, Lease, RecurringBilling, SalesOverage } from '../types/financial';

/**
 * 数据清理工具类
 * 负责清理和初始化财务系统相关的数据结构
 */
export class DataCleaner {
    /**
     * 清理 FinancialData 数据
     * @param data 要清理的 FinancialData 对象
     * @returns 清理后的 FinancialData 对象
     */
    static cleanFinancialData(data: FinancialData | null): FinancialData {
        // 如果数据为空，则返回默认值
        if (!data) {
            return this.getDefaultFinancialData();
        }

        // 提取 finance 对象，并使用默认值补全缺失字段
        const finance = {
            ...data.finance,
            objectId: data.finance.objectId || '',
            buCode: data.finance.buCode || '',
            buName: data.finance.buName || '',
            finMarket: data.finance.finMarket || '',
            contractType: data.finance.contractType || '',
            strategicAlliance: data.finance.strategicAlliance || '1',
            bizType: data.finance.bizType || '',
            partyA: data.finance.partyA || '',
            addOfPartyA: data.finance.addOfPartyA || '',
            partyB: data.finance.partyB || '',
            addOfPartyB: data.finance.addOfPartyB || '',
            addOfStore: data.finance.addOfStore || '',
            signDate: data.finance.signDate || '',
            openDate: data.finance.openDate || '',
            estimateOpen: data.finance.estimateOpen || '',
            returnAreaYes: data.finance.returnAreaYes || '',
            noSublet: data.finance.noSublet || 'N',
            areaSM: data.finance.areaSM || '',
            areaRemark: data.finance.areaRemark || '',
            certEffectiveDate: data.finance.certEffectiveDate || '',
            certExpiryDate: data.finance.certExpiryDate || '',
            leaseTypePlanning: data.finance.leaseTypePlanning || '',
            leaseTypeDE: data.finance.leaseTypeDE || '',
            leaseTypeRE: data.finance.leaseTypeRE || 'R2',
            depositAmount: data.finance.depositAmount || '',
            idcAmount: data.finance.idcAmount || '',
            leaseIncentives: data.finance.leaseIncentives || '',
            transferSubject: data.finance.transferSubject || 'N',
            reNotes: data.finance.reNotes || '',
            bondAmount: data.finance.bondAmount || '',
            bondReturnDate: data.finance.bondReturnDate || '',
            guaranteeAmount: data.finance.guaranteeAmount || '',
            leaseDetails: Array.isArray(data.finance.leaseDetails) ? 
                this.cleanLeaseDetails(data.finance.leaseDetails) : [],
            changeContType: data.finance.changeContType || ''
        };

        // 返回清理后的 FinancialData 对象
        return { finance };
    }

    /**
     * 合并对象，确保空字符串使用默认值
     * @param defaultObj 默认对象
     * @param userObj 用户提供的对象
     * @param arrayFields 需要特殊处理的数组字段
     * @returns 合并后的对象
     */
    private static mergeWithDefaults<T>(defaultObj: T, userObj: T | undefined, arrayFields: {[key: string]: string} = {}): T {
        const result = { ...defaultObj };
        
        if (!userObj) return result;
        
        // 处理普通字段
        for (const key in userObj) {
            // 跳过数组字段和objectId字段
            if (Object.values(arrayFields).includes(key) || key === 'objectId') continue;
            
            // 如果用户值不是空字符串，则使用用户值
            if ((userObj as any)[key] !== '' && (userObj as any)[key] !== null && (userObj as any)[key] !== undefined) {
                (result as any)[key] = (userObj as any)[key];
            }
        }
        
        // 处理objectId字段
        if (userObj && typeof userObj === 'object' && 'objectId' in userObj) {
            (result as any).objectId = (userObj as any).objectId || '';
        }
        
        // 处理数组字段
        for (const objKey in arrayFields) {
            const arrayKey = arrayFields[objKey];
            if (userObj && typeof userObj === 'object' && arrayKey in userObj && Array.isArray((userObj as any)[arrayKey])) {
                // 获取用户提供的数组
                const userArray = (userObj as any)[arrayKey];
                
                // 如果数组为空，使用默认值
                if (userArray.length === 0) {
                    (result as any)[arrayKey] = (defaultObj as any)[arrayKey];
                    continue;
                }
                
                // 处理数组中的每个元素
                if (arrayKey === 'phases') {
                    // 处理 phases 数组
                    (result as any)[arrayKey] = userArray.map((phase: any) => {
                        return this.mergeWithDefaults(this.getDefaultRbPhase(), phase);
                    });
                } else if (arrayKey === 'sovRules') {
                    // 处理 sovRules 数组
                    (result as any)[arrayKey] = userArray.map((rule: any) => {
                        return this.mergeWithDefaults(this.getDefaultSovRule(), rule);
                    });
                } else {
                    // 其他数组，直接使用用户值
                    (result as any)[arrayKey] = userArray;
                }
            }
        }
        
        return result;
    }

    /**
     * 清理 LeaseDetail 数组，确保每个条目字段完整
     * @param leaseDetails 要清理的 LeaseDetail 数组
     * @returns 清理后的 LeaseDetail 数组
     */
    static cleanLeaseDetails(leaseDetails: LeaseDetail[]): LeaseDetail[] {
        return leaseDetails.map(detail => {
            const defaultDetail = this.getDefaultLeaseDetail();
            
            return {
                objectId: detail.objectId || '',
                lease: this.mergeWithDefaults(defaultDetail.lease, detail.lease) as Lease,
                rb: this.mergeWithDefaults(defaultDetail.rb, detail.rb, { rb: 'phases' }) as RecurringBilling,
                sov: this.mergeWithDefaults(defaultDetail.sov, detail.sov, { sov: 'sovRules' }) as SalesOverage
            };
        });
    }

    /**
     * 获取默认的 FinancialData 数据
     */
    static getDefaultFinancialData(): FinancialData {
        return {
            finance: {
                objectId:'',//唯一标识
                buCode: '',
                buName: '',
                finMarket: '',
                contractType: '',
                strategicAlliance: '1',
                bizType: '',
                partyA: '',
                addOfPartyA: '',
                partyB: '',
                addOfPartyB: '',
                addOfStore: '',
                signDate: '',
                openDate: '',
                estimateOpen: '',
                returnAreaYes: '',
                noSublet: 'N',
                areaSM: '',
                areaRemark: '',
                certEffectiveDate: '',
                certExpiryDate: '',
                leaseTypePlanning: '',
                leaseTypeDE: '',
                leaseTypeRE: 'R2',
                depositAmount: '',
                idcAmount: '',
                leaseIncentives: '',
                transferSubject: 'N',
                reNotes: '',
                bondAmount: '',
                bondReturnDate: '',
                guaranteeAmount: '',
                leaseDetails: [],
                changeContType: ''
            },
        };
    }

    /**
     * 获取默认的 LeaseDetail 数据
     * @returns 默认的 LeaseDetail 对象
     */
    static getDefaultLeaseDetail(): LeaseDetail {
        return {
            objectId: '',
            lease: {
                objectId:'',//唯一标识
                leaseCode: '',
                useOfLeaseNo: '',
                rentName: '',
                rentType: 'RS',
                landlordCode: '',
                landlordName: '',
                receivedCode: '',
                receivedName: '',
                taxplayerJde: '',
                taxplayerName: '',
                mergeOthersYes: 'N',
                leaseGroup: '',
                strategyAlianceYes: 'N',
                rentAreaYes: 'N',
                yearlyNote: 'N',
                receiverNote: '',
                monthlyNote: 'N',
                unit: '',
                beginDate: '',
                freeBeginDate: '',
                freeEndDate: '',
                startRentDate: '',
                orgEndDate: '',
                salesCalculation: '',
                relation: ''
            },
            rb: {
                objectId:'',//唯一标识
                rbPaymentFrequency: 'M',
                rbPaymentType: '',
                paymentRent: '0',
                dba: '',
                paymentEarlyRentYesDay: '',
                paymentLease: '',
                rbFeeDesc: '',
                rbAnnualSettleYes: '',
                rentAnnual: '',
                rbtrRent: '',
                rbtrMgmt: '',
                rbtrAdv: '',
                rbtrFee: '',
                relation: '',
                phases: []
            },
            sov: {
                objectId:'',//唯一标识
                sovBreakMonthYes: 'N',
                sovLeaseVAT: '',
                calculationMethod: '',
                sovPaymentType: '',
                minMaxNeedYes: 'N',
                minMaxType1: '',
                minMaxType2: '',
                sovRules: []
            }
        };
    }

    /**
     * 获取默认的 RbPhase 数据
     */
    static getDefaultRbPhase(): RbPhase {
        return {
            objectId: '',
            rbId: '',
            rbRateNum: 0,
            rbBeginDate: '',
            rbEndDate: '',
            rbAccruedAmountRent: '',
            rbAccruedAmountMgmt: '',
            rbAccruedAmountAdv: '',
            rbAccruedAmountFee: '',
            rbPaymentAmountRent: '',
            rbPaymentAmountMgmt: '',
            rbPaymentAmountAdv: '',
            rbPaymentAmountFee: ''
        };
    }

    /**
     * 获取默认的 SovRule 数据
     */
    static getDefaultSovRule(): SovRule {
        return {
            objectId: '',
            sovId: '',
            sovIndexNo: 0,
            sovAnnualSettleYes: '',
            annualSettleFrequency: '',
            annualSettleMonth: '1',
            sovBeginDate: '',
            sovEndDate: '',
            paymentOnSovYes: 'Y',
            sovRuleList: []
        };
    }
} 