import { LeaseDetail, Finance, ValidationResult, ValidationErrorDetail, ValidationWarningDetail } from '../types/financial';
import { DOMUtils } from './DOMUtils.ts';

/**
 * 验证器类，负责处理所有表单验证逻辑
 */
export class Validator {
    private warningDetails: Array<{ index: number; message: string[] }> = [];

    /**
     * 验证 BU 基本信息
     * @param finance Finance 对象
     * @param result 验证结果对象
     */
    public validateBuInfoFromDOM(finance: Finance, result: ValidationResult): void {
        // 获取 BU 信息容器
        const buContainer = document.getElementById('buContainer');
        if (!buContainer) return;

        // 查找所有带有 x-model 属性的输入元素
        const inputs = buContainer.querySelectorAll('input[x-model], select[x-model]');
        
        inputs.forEach(input => {
            const xModel = input.getAttribute('x-model');
            if (!xModel || !xModel.startsWith('data.finance.')) return;
            
            // 提取字段名
            const fieldName = xModel.replace('data.finance.', '');
            const value = finance[fieldName as keyof Finance];
            
            let hasError = false;
            
            // 验证必填字段
            if (input.hasAttribute('required') && !value) {
                result.isValid = false;
                result.buErrors.push({
                    path: `finance.${fieldName}`,
                    message: `${this.getFieldLabel(input)} 不能为空`,
                    element: input
                });
                hasError = true;
            }
            
            // 如果值为空，不进行格式验证
            if (!value) {
                if (hasError) {
                    this.markElementAsError(input);
                }
                return;
            }
            
            // 验证格式
            const dataInit = input.getAttribute('data-init');
            if (dataInit) {
                try {
                    const config = DOMUtils.parseDataInit(dataInit);
                    
                    // 验证日期格式
                    if (config.dateTime) {
                        if (!/^\d{4}-\d{2}-\d{2}$/.test(value as string)) {
                            result.isValid = false;
                            result.buErrors.push({
                                path: `finance.${fieldName}`,
                                message: `${this.getFieldLabel(input)} 日期格式不正确，应为 YYYY-MM-DD`,
                                element: input
                            });
                            hasError = true;
                        }
                    }
                    
                    // 验证数字格式
                    if (config.alias === 'numeric' || config.alias === 'currency' || config.alias === 'decimal') {
                        const regex = config.regex || /^\d+(\.\d{1,2})?$/;
                        if (!new RegExp(regex).test(value as string)) {
                            result.isValid = false;
                            result.buErrors.push({
                                path: `finance.${fieldName}`,
                                message: config.warningMessage || `${this.getFieldLabel(input)} 格式不正确`,
                                element: input
                            });
                            hasError = true;
                        }
                        
                        // 验证最小值和最大值
                        if (config.min !== undefined && parseFloat(value as string) < config.min) {
                            result.isValid = false;
                            result.buErrors.push({
                                path: `finance.${fieldName}`,
                                message: `${this.getFieldLabel(input)} 不能小于 ${config.min}`,
                                element: input
                            });
                            hasError = true;
                        }
                        
                        if (config.max !== undefined && parseFloat(value as string) > config.max) {
                            result.isValid = false;
                            result.buErrors.push({
                                path: `finance.${fieldName}`,
                                message: `${this.getFieldLabel(input)} 不能大于 ${config.max}`,
                                element: input
                            });
                            hasError = true;
                        }
                    }
                    
                    // 处理警告
                    if (config.warning && !new RegExp(config.warning).test(value as string)) {
                        result.buWarnings.push({
                            path: `finance.${fieldName}`,
                            message: config.warningMessage || `${this.getFieldLabel(input)} 格式不符合建议规范`,
                            element: input
                        });
                    }
                } catch (e) {
                    console.error('Failed to parse data-init config:', e);
                }
            }
            
            // 如果有错误，标记元素
            if (hasError) {
                this.markElementAsError(input);
            }
        });
        
        // 验证日期逻辑
        this.validateDateLogic(finance, result);
    }

    /**
     * 验证 LeaseDetail 数据
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @returns 验证错误和警告
     */
    public validateLeaseDetailFromDOM(detail: LeaseDetail, index: number): {
        errors: ValidationErrorDetail[];
        warnings: ValidationWarningDetail[];
    } {
        const errors: ValidationErrorDetail[] = [];
        const warnings: ValidationWarningDetail[] = [];
        
        // 验证 Lease 部分
        this.validateLeaseSection(detail, index, errors, warnings);
        
        // 验证 RB 部分
        this.validateRbSection(detail, index, errors, warnings);
        
        // 验证 SOV 部分
        this.validateSovSection(detail, index, errors, warnings);
        
        return { errors, warnings };
    }

    /**
     * 验证 Lease 部分
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateLeaseSection(detail: LeaseDetail, index: number, errors: ValidationErrorDetail[], warnings: ValidationWarningDetail[]): void {
        if (!detail.lease) return;
        
        // 获取 LeaseDetail_lease 容器
        const leaseContainer = document.getElementById('LeaseDetail_lease');
        if (!leaseContainer) return;
        
        // 1. 验证基本字段
        this.validateBasicFields(detail, index, leaseContainer, 'lease', errors, warnings);
        
        // 2. 执行特定的业务逻辑验证
        //this.validateLeaseBusinessLogic(detail, index, errors);
    }

    /**
     * 验证 RB 部分
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateRbSection(detail: LeaseDetail, index: number, errors: ValidationErrorDetail[], warnings: ValidationWarningDetail[]): void {
        if (!detail.rb) return;
        
        // 获取 LeaseDetail_br 容器
        const rbContainer = document.getElementById('LeaseDetail_br');
        if (!rbContainer) return;
        
        // 1. 验证基本字段
        this.validateBasicFields(detail, index, rbContainer, 'rb', errors, warnings);
        
        // 2. 验证 phases 部分
        this.validateRbPhases(detail, index, errors, warnings);
    }

    /**
     * 验证 SOV 部分
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateSovSection(detail: LeaseDetail, index: number, errors: ValidationErrorDetail[], warnings: ValidationWarningDetail[]): void {
        if (!detail.sov) return;
        
        // 获取 LeaseDetail_sov 容器
        const sovContainer = document.getElementById('LeaseDetail_sov');
        if (!sovContainer) return;
        
        // 1. 验证基本字段
        this.validateBasicFields(detail, index, sovContainer, 'sov', errors, warnings);
        
        // 2. 验证 sovRules 部分
        this.validateSovRules(detail, index, errors, warnings);
    }

    /**
     * 通用方法：验证基本字段（包括处理 template 元素）
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @param container 容器元素
     * @param section 部分名称 ('lease', 'rb', 或 'sov')
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateBasicFields(
        detail: LeaseDetail, 
        index: number, 
        container: HTMLElement, 
        section: 'lease' | 'rb' | 'sov', 
        errors: ValidationErrorDetail[], 
        warnings: ValidationWarningDetail[]
    ): void {
        // 用于记录已处理的字段
        const processedFields = new Set<string>();
        
        // 1. 先处理 template 内的元素
        this.processTemplateElements(detail, index, container, section, errors, warnings, processedFields);
        
        // 2. 处理非 template 内的输入元素（排除已处理的字段）
        this.processNonTemplateElements(detail, index, container, section, errors, warnings, processedFields);
    }

    /**
     * 处理 template 标签内的表单元素
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @param container 容器元素
     * @param section 部分名称 ('lease', 'rb', 或 'sov')
     * @param errors 错误数组
     * @param warnings 警告数组
     * @param processedFields 已处理的字段集合
     */
    private processTemplateElements(
        detail: LeaseDetail, 
        index: number, 
        container: HTMLElement, 
        section: 'lease' | 'rb' | 'sov', 
        errors: ValidationErrorDetail[], 
        warnings: ValidationWarningDetail[],
        processedFields: Set<string>
    ): void {
        // 查找所有 template 标签
        const templates = container.querySelectorAll('template');
        
        templates.forEach(template => {
            // 获取条件表达式
            const condition = template.getAttribute('x-if');
            if (!condition) return;
            
            // 评估条件是否满足
            const conditionMet = this.evaluateCondition(condition, detail);
            
            // 如果条件满足，验证 template 内的元素
            if (conditionMet) {
                const inputs = (template as HTMLTemplateElement).content.querySelectorAll(`input[x-model], select[x-model]`);
                
                inputs.forEach(input => {
                    const xModel = input.getAttribute('x-model');
                    if (!xModel || !xModel.startsWith(`currentLeaseDetail.${section}.`)) return;
                    
                    // 提取字段名
                    const field = xModel.replace(`currentLeaseDetail.${section}.`, '');
                    
                    // 记录已处理的字段
                    processedFields.add(field);
                    
                    // 验证字段
                    this.validateField(detail, index, section, field, input, errors, warnings);
                });
            }
        });
    }

    /**
     * 处理非 template 标签内的表单元素
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @param container 容器元素
     * @param section 部分名称 ('lease', 'rb', 或 'sov')
     * @param errors 错误数组
     * @param warnings 警告数组
     * @param processedFields 已处理的字段集合
     */
    private processNonTemplateElements(
        detail: LeaseDetail, 
        index: number, 
        container: HTMLElement, 
        section: 'lease' | 'rb' | 'sov', 
        errors: ValidationErrorDetail[], 
        warnings: ValidationWarningDetail[],
        processedFields: Set<string>
    ): void {
        // 查找所有非 template 内的输入元素
        const inputs = container.querySelectorAll(`.po-child-content > .w-public-input input[x-model], .po-child-content > .w-public-input select[x-model]`);
        
        inputs.forEach(input => {
            // 跳过 template 内的元素
            if (input.closest('template')) return;
            
            const xModel = input.getAttribute('x-model');
            if (!xModel || !xModel.startsWith(`currentLeaseDetail.${section}.`)) return;
            
            // 提取字段名
            const field = xModel.replace(`currentLeaseDetail.${section}.`, '');
            
            // 如果字段已经在 template 中处理过，跳过
            if (processedFields.has(field)) return;
            
            // 验证字段
            this.validateField(detail, index, section, field, input, errors, warnings);
        });
    }

    /**
     * 验证单个字段
     * @param detail LeaseDetail 对象
     * @param index LeaseDetail 的索引
     * @param section 部分名称 ('lease', 'rb', 或 'sov')
     * @param field 字段名
     * @param input 输入元素
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateField(
        detail: LeaseDetail, 
        index: number, 
        section: 'lease' | 'rb' | 'sov', 
        field: string, 
        input: Element, 
        errors: ValidationErrorDetail[], 
        warnings: ValidationWarningDetail[]
    ): void {
        // 获取标签文本
        let label = field;
        const labelElement = input.closest('.w-public-input')?.querySelector('label');
        if (labelElement) {
            label = labelElement.textContent?.replace(/\*/g, '').trim() || field;
        }
        
        // 检查是否必填
        const required = input.hasAttribute('required');
        
        // 特殊处理 leaseGroup 字段
        if (section === 'lease' && field === 'leaseGroup') {
            // 只有当 mergeOthersYes 为 'Y' 时才验证 leaseGroup
            if (detail.lease?.mergeOthersYes !== 'Y') {
                return; // 跳过验证
            }
        }
        
        // 特殊处理 rbFeeDesc 字段 - 只有当 useOfLeaseNo 为 '3' 时才验证
        if (section === 'rb' && field === 'rbFeeDesc') {
            if (detail.lease?.useOfLeaseNo !== '3') {
                return; // 跳过验证
            }
        }
        
        // 特殊处理税率字段 - 根据 useOfLeaseNo 决定验证哪些税率字段
        if (section === 'rb') {
            const useOfLeaseNo = detail.lease?.useOfLeaseNo;
            
            if (field === 'rbtrRent' && useOfLeaseNo !== '0' && useOfLeaseNo !== '4') {
                return; // 跳过验证
            }
            
            if (field === 'rbtrMgmt' && useOfLeaseNo !== '1' && useOfLeaseNo !== '4') {
                return; // 跳过验证
            }
            
            if (field === 'rbtrAdv' && useOfLeaseNo !== '2') {
                return; // 跳过验证
            }
            
            if (field === 'rbtrFee' && useOfLeaseNo !== '3') {
                return; // 跳过验证
            }
        }
        
        // 获取字段值
        const value = (detail[section] as any)[field];
        
        // 必填字段验证
        if (required && !value) {
            errors.push({
                path: `leaseDetails[${index}].${section}.${field}`,
                message: `${label} 不能为空`
            });
            return;
        }
        
        // 如果值为空，不进行后续验证
        if (!value) return;
        
        // 提取验证规则
        const dataInit = input.getAttribute('data-init');
        if (dataInit) {
            try {
                const config = DOMUtils.parseDataInit(dataInit);
                
                // 正则验证
                if (config.regex) {
                    const regex = new RegExp(config.regex);
                    
                    if (!regex.test(value)) {
                        errors.push({
                            path: `leaseDetails[${index}].${section}.${field}`,
                            message: config.regexMessage ||  `${label} 格式不正确`
                        });
                    }
                }
                
                // 警告验证
                if (config.warning) {
                    const warningRegex = new RegExp(config.warning);
                    if (!warningRegex.test(value)) {
                        warnings.push({
                            path: `leaseDetails[${index}].${section}.${field}`,
                            message: config.warningMessage || `${label} 格式不符合建议规范`
                        });
                    }
                }
            } catch (e) {
                console.error('Failed to parse data-init config:', e);
            }
        }
    }

    /**
     * 验证日期逻辑
     * @param finance Finance 对象
     * @param result 验证结果对象
     */
    private validateDateLogic(finance: Finance, result: ValidationResult): void {
        // 验证签约日期和开业日期
        if (finance.signDate && finance.openDate) {
            const signDate = new Date(finance.signDate);
            const openDate = new Date(finance.openDate);
            
            if (signDate > openDate) {
                result.isValid = false;
                result.buErrors.push({
                    path: 'finance.openDate',
                    message: '开业日期不能早于签约日期',
                    element: document.querySelector('input[x-model="data.finance.openDate"]') as Element
                });
            }
        }
        
        // 验证房产证日期
        if (finance.certEffectiveDate && finance.certExpiryDate) {
            const effectiveDate = new Date(finance.certEffectiveDate);
            const expiryDate = new Date(finance.certExpiryDate);
            
            if (effectiveDate > expiryDate) {
                result.isValid = false;
                result.buErrors.push({
                    path: 'finance.certExpiryDate',
                    message: '房产证到期日不能早于生效日',
                    element: document.querySelector('input[x-model="data.finance.certExpiryDate"]') as Element
                });
            }
        }
    }

    /**
     * 获取字段标签
     * @param element 表单元素
     * @returns 字段标签文本
     */
    private getFieldLabel(element: Element): string {
        const labelElement = element.closest('.w-public-input')?.querySelector('label');
        if (labelElement) {
            return labelElement.textContent?.replace(/\*/g, '').trim() || '';
        }
        return '';
    }

    /**
     * 标记元素为错误状态
     * @param element 要标记的元素
     */
    public markElementAsError(element: Element): void {
        const container = element.closest('.w-public-input');
        if (container) {
            container.classList.add('error');
        }
    }

    /**
     * 获取警告详情
     * @returns 警告详情数组
     */
    public getWarningDetails(): Array<{ index: number; message: string[] }> {
        return this.warningDetails;
    }

    /**
     * 清除警告详情
     */
    public clearWarningDetails(): void {
        this.warningDetails = [];
    }

    /**
     * 验证租赁详情中的phases
     * @param detail 租赁详情
     * @param index 租赁详情索引
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateRbPhases(detail: LeaseDetail, index: number, errors: ValidationErrorDetail[], warnings: ValidationWarningDetail[]): void {
        // 获取 phases 容器
        const phasesContainer = document.querySelector('#LeaseDetail_br .po-child-content-hasPhase');
        if (!phasesContainer) return;
        
        // 如果没有 phases 数据，检查是否需要添加错误
        if (!detail.rb?.phases || detail.rb.phases.length === 0) {
            return;
        }
        
        // 获取 phase 模板结构 - 只获取模板中的结构，而不是已渲染的元素
        const phaseTemplate = phasesContainer.querySelector('template');
        if (!phaseTemplate) {
            console.warn('Phase template not found');
            return;
        }
        
        // 从模板内容中获取 .phase-child 元素
        const phaseChildTemplate = (phaseTemplate as HTMLTemplateElement).content.querySelector('.phase-child');
        if (!phaseChildTemplate) {
            console.warn('.phase-child element not found in template');
            return;
        }
        
        // 验证每个 phase
        detail.rb.phases.forEach((phase, phaseIndex) => {
            // console.log(`Validating phase ${phaseIndex}:`, phase);
            
            // 从页面提取该 phase 的验证规则
            const phaseRules = this.extractPhaseValidationRules(phaseChildTemplate, detail.lease?.useOfLeaseNo);
            
            // 应用验证规则
            this.applyPhaseValidationRules(phase, phaseIndex, index, phaseRules, errors, warnings);
        });
    }

    /**
     * 应用phase验证规则
     * @param phase Phase对象
     * @param phaseIndex Phase索引
     * @param leaseIndex 租赁详情索引
     * @param rules 验证规则
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private applyPhaseValidationRules(phase: any, phaseIndex: number, leaseIndex: number, rules: any[], errors: ValidationErrorDetail[], _warnings: ValidationWarningDetail[]): void {
        // 检查必填字段
        rules.forEach(rule => {
            const fieldName = rule.field;
            const value = phase[fieldName];

            //(`Checking field ${fieldName} with value:`, value);

            // 检查必填字段
            if (rule.required && (!value || value.toString().trim() === '')) {
                errors.push({
                    path: `leaseDetails[${leaseIndex}].rb.phases[${phaseIndex}].${fieldName}`,
                    message: `${rule.label} 不能为空`
                });
                return; // 如果字段为空，不进行后续格式验证
            }

            // 如果值为空且不是必填字段，跳过格式验证
            if (!value || value.toString().trim() === '') {
                return;
            }

            // 检查日期格式（针对日期字段）
            if (rule.isDateField && !this.isValidDateFormat(value)) {
                errors.push({
                    path: `leaseDetails[${leaseIndex}].rb.phases[${phaseIndex}].${fieldName}`,
                    message: `${rule.label} 日期格式不正确，应为 YYYY-MM-DD`
                });
            }

            // 验证日期逻辑关系（开始日期不能晚于结束日期）
            if (fieldName === 'rbBeginDate' || fieldName === 'rbEndDate') {
                this.validatePhaseDateLogic(phase, phaseIndex, leaseIndex, errors);
            }

            // 其他验证规则...
        });
    }

    /**
     * 从phase模板中提取验证规则
     * @param phaseTemplate phase模板元素
     * @param useOfLeaseNo 租赁用途编号
     * @returns 验证规则数组
     */
    private extractPhaseValidationRules(phaseTemplate: Element | null, useOfLeaseNo: string | undefined): any[] {
        const rules: any[] = [];
        
        if (!phaseTemplate) return rules;
        
        // 提取基本字段的验证规则
        const dateInputs = phaseTemplate.querySelectorAll('input.w-form-control-date');
        dateInputs.forEach(input => {
            const xModel = input.getAttribute('x-model');
            if (xModel) {
                const fieldName = xModel.replace('phase.', '');
                const label = this.getInputLabel(input);
                rules.push({
                    field: fieldName,
                    label: label,
                    required: input.hasAttribute('required'),
                    isDateField: true // 标识这是一个日期字段
                });
            }
        });
        
        // 根据租赁用途提取特定字段的验证规则
        if (useOfLeaseNo === '0' || useOfLeaseNo === '4') {
            // 固定租金相关字段
            this.extractFieldRulesByUseOfLease(phaseTemplate, 'rbAccruedAmountRent', 'rbPaymentAmountRent', rules);
        }
        
        if (useOfLeaseNo === '1' || useOfLeaseNo === '4') {
            // 物管费相关字段
            this.extractFieldRulesByUseOfLease(phaseTemplate, 'rbAccruedAmountMgmt', 'rbPaymentAmountMgmt', rules);
        }
        
        if (useOfLeaseNo === '2') {
            // 广告费相关字段
            this.extractFieldRulesByUseOfLease(phaseTemplate, 'rbAccruedAmountAdv', 'rbPaymentAmountAdv', rules);
        }
        
        if (useOfLeaseNo === '3') {
            // 其他费用相关字段
            this.extractFieldRulesByUseOfLease(phaseTemplate, 'rbAccruedAmountFee', 'rbPaymentAmountFee', rules);
        }
        
        //console.log('Extracted phase validation rules:', rules);
        
        return rules;
    }

    /**
     * 根据租赁用途提取字段规则
     * @param phaseTemplate 阶段模板元素
     * @param accruedField 计提字段名
     * @param paymentField 付款字段名
     * @param rules 规则数组
     */
    private extractFieldRulesByUseOfLease(_phaseTemplate: Element, accruedField: string, paymentField: string, rules: any[]): void {
        // 根据字段名确定标签文本和是否必填
        let accruedLabel = '';
        let paymentLabel = '';
        let isRequired = true;
        
        // 根据字段名确定标签文本
        switch (accruedField) {
            case 'rbAccruedAmountRent':
                accruedLabel = '固定租金计提（元）';
                break;
            case 'rbAccruedAmountMgmt':
                accruedLabel = '物管费计提（元）';
                break;
            case 'rbAccruedAmountAdv':
                accruedLabel = '广告费计提（元）';
                break;
            case 'rbAccruedAmountFee':
                accruedLabel = '其他费用计提（元）';
                break;
        }
        
        switch (paymentField) {
            case 'rbPaymentAmountRent':
                paymentLabel = '固定租金付款（元）';
                break;
            case 'rbPaymentAmountMgmt':
                paymentLabel = '物管费付款（元）';
                break;
            case 'rbPaymentAmountAdv':
                paymentLabel = '广告费付款（元）';
                break;
            case 'rbPaymentAmountFee':
                paymentLabel = '其他费用付款（元）';
                break;
        }
        
        // 添加计提字段规则
        if (accruedLabel) {
            rules.push({
                field: accruedField,
                label: accruedLabel,
                required: isRequired
            });
        }
        
        // 添加付款字段规则
        if (paymentLabel) {
            rules.push({
                field: paymentField,
                label: paymentLabel,
                required: isRequired
            });
        }
    }

    /**
     * 获取输入元素对应的标签文本
     * @param input 输入元素
     * @returns 标签文本
     */
    private getInputLabel(input: Element): string {
        const container = input.closest('.w-public-input');
        if (container) {
            const label = container.querySelector('.form-label');
            if (label) {
                // 移除 * 号和其他非文本内容
                return label.textContent?.replace(/\*/g, '').trim() || '';
            }
        }
        return '';
    }

    /**
     * 验证日期格式
     * @param value 日期字符串
     * @returns 是否是有效的日期格式
     */
    private isValidDateFormat(value: string): boolean {
        if (!value) return false;

        // 检查是否符合 YYYY-MM-DD 格式
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(value)) return false;

        // 检查是否是有效的日期
        try {
            const date = new Date(value);
            return !isNaN(date.getTime());
        } catch (e) {
            return false;
        }
    }

    /**
     * 验证阶段日期逻辑关系
     * @param phase 阶段对象
     * @param phaseIndex 阶段索引
     * @param leaseIndex 租赁详情索引
     * @param errors 错误数组
     */
    private validatePhaseDateLogic(phase: any, phaseIndex: number, leaseIndex: number, errors: ValidationErrorDetail[]): void {
        const beginDate = phase.rbBeginDate;
        const endDate = phase.rbEndDate;

        // 如果两个日期都存在且格式都正确，验证逻辑关系
        if (beginDate && endDate &&
            this.isValidDateFormat(beginDate) &&
            this.isValidDateFormat(endDate)) {

            const beginDateObj = new Date(beginDate);
            const endDateObj = new Date(endDate);

            if (beginDateObj > endDateObj) {
                errors.push({
                    path: `leaseDetails[${leaseIndex}].rb.phases[${phaseIndex}].rbEndDate`,
                    message: '结束日期不能早于开始日期'
                });
            }
        }
    }

    /**
     * 验证SOV规则
     * @param detail 租赁详情
     * @param index 租赁详情索引
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateSovRules(detail: LeaseDetail, index: number, errors: ValidationErrorDetail[], _warnings: ValidationWarningDetail[]): void {
        // 如果没有 sovRules 数据，直接返回
        if (!detail.sov?.sovRules || detail.sov.sovRules.length === 0) {
            return;
        }
        
        // 验证每个 sovRule
        detail.sov.sovRules.forEach((rule, ruleIndex) => {
            //console.log(`Validating sovRule ${ruleIndex}:`, rule);
            
            // 验证开始日期
            if (!rule.sovBeginDate) {
                errors.push({
                    path: `leaseDetails[${index}].sov.sovRules[${ruleIndex}].sovBeginDate`,
                    message: "抽成开始日期 不能为空"
                });
            } else if (!this.isValidDateFormat(rule.sovBeginDate)) {
                errors.push({
                    path: `leaseDetails[${index}].sov.sovRules[${ruleIndex}].sovBeginDate`,
                    message: "抽成开始日期 格式不正确"
                });
            }
            
            // 验证结束日期
            if (!rule.sovEndDate) {
                errors.push({
                    path: `leaseDetails[${index}].sov.sovRules[${ruleIndex}].sovEndDate`,
                    message: "抽成结束日期 不能为空"
                });
            } else if (!this.isValidDateFormat(rule.sovEndDate)) {
                errors.push({
                    path: `leaseDetails[${index}].sov.sovRules[${ruleIndex}].sovEndDate`,
                    message: "抽成结束日期 格式不正确"
                });
            }
            
            // 验证开始日期和结束日期的关系
            if (rule.sovBeginDate && rule.sovEndDate && this.isValidDateFormat(rule.sovBeginDate) && this.isValidDateFormat(rule.sovEndDate)) {
                const beginDate = new Date(rule.sovBeginDate);
                const endDate = new Date(rule.sovEndDate);
                
                if (beginDate > endDate) {
                    errors.push({
                        path: `leaseDetails[${index}].sov.sovRules[${ruleIndex}].sovEndDate`,
                        message: "抽成结束日期 不能早于开始日期"
                    });
                }
            }
        });
    }

    /**
     * 根据路径查找元素
     * @param path 字段路径，例如 "finance.buCode" 或 "leaseDetails[0].lease.leaseCode"
     * @param context 当前上下文，包含 Alpine 组件和根元素
     * @returns 对应的 DOM 元素，如果找不到则返回 null
     */
    public findElementByPath(path: string, context: { $el: HTMLElement, alpineComponent: any }): Element | null {
        // 处理 finance 字段
        if (path.startsWith('finance.')) {
            const fieldName = path.replace('finance.', '');
            return context.$el.querySelector(`[x-model="data.finance.${fieldName}"]`);
        }
        
        // 处理 leaseDetails 字段
        if (path.startsWith('leaseDetails[')) {
            // 提取索引和字段路径
            const matches = path.match(/leaseDetails\[(\d+)\]\.(.+)/);
            if (matches && matches.length === 3) {
                const leaseIndex = parseInt(matches[1]);
                const fieldPath = matches[2]; // 例如 "lease.leaseCode" 或 "rb.phases[0].rbBeginDate"
                
                // 当前正在编辑的 LeaseDetail
                if (leaseIndex === context.alpineComponent.indexLeaseDetail) {
                    // 处理 rb.phases 数组
                    if (fieldPath.includes('rb.phases[')) {
                        const phaseMatches = fieldPath.match(/rb\.phases\[(\d+)\]\.(.+)/);
                        if (phaseMatches && phaseMatches.length === 3) {
                            const phaseIndex = parseInt(phaseMatches[1]);
                            const phaseField = phaseMatches[2]; // 例如 "rbBeginDate"
                            
                            // 查找所有 phase 元素
                            const phaseElements = context.$el.querySelectorAll('#LeaseDetail_br .po-child-content-hasPhase ul li.phase-child');
                            
                            // 确保 phaseIndex 在有效范围内
                            if (phaseIndex >= 0 && phaseIndex < phaseElements.length) {
                                const phaseContainer = phaseElements[phaseIndex];
                                
                                // 在 phase 容器中查找对应的字段
                                // 首先尝试使用 x-model 属性查找
                                let inputElement = phaseContainer.querySelector(`[x-model="phase.${phaseField}"]`);
                                
                                // 如果没有找到，尝试使用 id 属性查找
                                if (!inputElement) {
                                    inputElement = phaseContainer.querySelector(`#${phaseField}${phaseIndex}`);
                                }
                                
                                // 如果还没有找到，尝试查找具有特定数据索引的输入元素
                                if (!inputElement) {
                                    const inputs = phaseContainer.querySelectorAll('input[data-index]');
                                    for (const input of inputs) {
                                        if (input.getAttribute('data-index') === phaseIndex.toString() && 
                                            input.getAttribute('x-model')?.includes(phaseField)) {
                                            inputElement = input;
                                            break;
                                        }
                                    }
                                }
                                
                                // 如果还没有找到，尝试查找任何可能匹配的输入元素
                                if (!inputElement) {
                                    const inputs = phaseContainer.querySelectorAll('input');
                                    for (const input of inputs) {
                                        const xModel = input.getAttribute('x-model');
                                        if (xModel && xModel.includes(phaseField)) {
                                            inputElement = input;
                                            break;
                                        }
                                    }
                                }
                                
                                return inputElement;
                            }
                        }
                    }
                    // 处理 sov.sovRules 数组
                    else if (fieldPath.includes('sov.sovRules[')) {
                        // 处理 sovRules[0].sovRuleList[0].breakpoint 这样的嵌套数组
                        const sovRuleListMatches = fieldPath.match(/sov\.sovRules\[(\d+)\]\.sovRuleList\[(\d+)\]\.(.+)/);
                        if (sovRuleListMatches && sovRuleListMatches.length === 4) {
                            const ruleIndex = parseInt(sovRuleListMatches[1]);
                            const ruleListIndex = parseInt(sovRuleListMatches[2]);
                            const ruleListField = sovRuleListMatches[3]; // 例如 "breakpoint"
                            
                            // 查找所有 rule 元素
                            const ruleElements = context.$el.querySelectorAll('#LeaseDetail_sov .po-child-content-hasPhase ul li.phase-child');
                            
                            // 确保 ruleIndex 在有效范围内
                            if (ruleIndex >= 0 && ruleIndex < ruleElements.length) {
                                const ruleContainer = ruleElements[ruleIndex];
                                
                                // 查找表格中的行
                                const rows = ruleContainer.querySelectorAll('.po-child-ratio table tbody tr');
                                
                                if (ruleListIndex >= 0 && ruleListIndex < rows.length) {
                                    const row = rows[ruleListIndex];
                                    
                                    // 在行中查找对应的字段
                                    const inputs = row.querySelectorAll('input');
                                    
                                    for (const input of inputs) {
                                        const xModel = input.getAttribute('x-model');
                                        
                                        if (xModel && xModel.includes(ruleListField)) {
                                            return input;
                                        }
                                    }
                                    
                                    // 如果没有找到精确匹配，返回第一个输入元素
                                    if (inputs.length > 0) {
                                        return inputs[0];
                                    }
                                }
                                
                                // 如果找不到特定行，标记整个表格
                                const tableContainer = ruleContainer.querySelector('.po-child-ratio');
                                if (tableContainer) {
                                    return tableContainer;
                                }
                            }
                        }
                        // 处理 sovRules[0].sovBeginDate 这样的普通字段
                        else {
                            const ruleMatches = fieldPath.match(/sov\.sovRules\[(\d+)\]\.(.+)/);
                            if (ruleMatches && ruleMatches.length === 3) {
                                const ruleIndex = parseInt(ruleMatches[1]);
                                const ruleField = ruleMatches[2]; // 例如 "sovBeginDate"
                                
                                // 查找所有 rule 元素
                                const ruleElements = context.$el.querySelectorAll('#LeaseDetail_sov .po-child-content-hasPhase ul li.phase-child');
                                
                                // 确保 ruleIndex 在有效范围内
                                if (ruleIndex >= 0 && ruleIndex < ruleElements.length) {
                                    const ruleContainer = ruleElements[ruleIndex];
                                    
                                    // 特殊处理 sovRuleList 字段
                                    if (ruleField === 'sovRuleList') {
                                        // 对于 sovRuleList 错误，我们标记整个表格容器
                                        const tableContainer = ruleContainer.querySelector('.po-child-ratio');
                                        if (tableContainer) {
                                            return tableContainer;
                                        }
                                    }
                                    
                                    // 在 rule 容器中查找对应的字段
                                    // 首先尝试使用 x-model 属性查找
                                    let inputElement = ruleContainer.querySelector(`[x-model="rule.${ruleField}"]`);
                                    
                                    // 如果没有找到，尝试使用 id 属性查找
                                    if (!inputElement) {
                                        try {
                                            inputElement = ruleContainer.querySelector(`#${ruleField}${ruleIndex}`);
                                        } catch (e) {
                                            console.error(`Invalid selector: #${ruleField}${ruleIndex}`, e);
                                        }
                                    }
                                    
                                    // 如果还没有找到，尝试查找任何可能匹配的输入元素
                                    if (!inputElement) {
                                        const inputs = ruleContainer.querySelectorAll('input, select');
                                        for (const input of inputs) {
                                            const xModel = input.getAttribute('x-model');
                                            if (xModel && xModel.includes(ruleField)) {
                                                inputElement = input;
                                                break;
                                            }
                                        }
                                    }
                                    
                                    return inputElement;
                                }
                            }
                        }
                    }
                    else {
                        // 非数组的字段
                        return context.$el.querySelector(`[x-model="currentLeaseDetail.${fieldPath}"]`);
                    }
                }
            }
        }
        
        return null;
    }

    /**
     * 验证字段值
     * @param element 表单元素
     * @param value 字段值
     * @returns 验证结果，包含是否有效、是否有警告以及消息
     */
    public validateFieldValue(element: Element, value: any): { isValid: boolean; warning?: boolean; message?: string } {
        // 获取 data-init 属性
        const dataInit = element.getAttribute('data-init');
        if (!dataInit) {
            // 如果没有 data-init 属性，只检查必填项
            if (element.hasAttribute('required') && (!value || value.toString().trim() === '')) {
                const label = this.getInputLabel(element);
                return { isValid: false, message: `${label} 不能为空` };
            }
            return { isValid: true };
        }
        
        try {
            // 使用 DOMUtils.parseDataInit 而不是直接 JSON.parse
            const initConfig = DOMUtils.parseDataInit(dataInit);
            
            // 检查必填项
            if (element.hasAttribute('required') && (!value || value.toString().trim() === '')) {
                const label = this.getInputLabel(element);
                return { isValid: false, message: `${label} 不能为空` };
            }
            
            // 如果值为空且不是必填项，则跳过其他验证
            if (!value || value.toString().trim() === '') {
                return { isValid: true };
            }
            
            // 检查日期格式
            if (initConfig.dateTime) {
                // 使用日期特定的正则表达式或默认的日期格式
                const dateRegex = initConfig.regex ? 
                    new RegExp(initConfig.regex.replace(/^\/|\/$/g, '')) : 
                    /^\d{4}-\d{2}-\d{2}$/;
                    
                if (!dateRegex.test(value)) {
                    const label = this.getInputLabel(element);
                    return { isValid: false, message: `${label} 日期格式不正确` };
                }
                
                // 验证日期是否有效
                try {
                    const date = new Date(value);
                    if (isNaN(date.getTime())) {
                        const label = this.getInputLabel(element);
                        return { isValid: false, message: `${label} 不是有效的日期` };
                    }
                } catch (e) {
                    const label = this.getInputLabel(element);
                    return { isValid: false, message: `${label} 不是有效的日期` };
                }
            }
            // 检查正则表达式验证（非日期字段）
            else if (initConfig.regex) {
                const regex = new RegExp(initConfig.regex.replace(/^\/|\/$/g, ''));
                if (!regex.test(value)) {
                    const label = this.getInputLabel(element);
                    return { isValid: false, message: initConfig.regexMessage || `${label} 格式不正确` };
                }
            }
            
            // 检查数字范围
            if (initConfig.alias === 'numeric' || initConfig.alias === 'currency') {
                const numValue = parseFloat(value);
                if (isNaN(numValue)) {
                    const label = this.getInputLabel(element);
                    return { isValid: false, message: `${label} 必须是数字` };
                }
                
                if (initConfig.min !== undefined && numValue < initConfig.min) {
                    const label = this.getInputLabel(element);
                    return { isValid: false, message: `${label} 不能小于 ${initConfig.min}` };
                }
                
                if (initConfig.max !== undefined && numValue > initConfig.max) {
                    const label = this.getInputLabel(element);
                    return { isValid: false, message: `${label} 不能大于 ${initConfig.max}` };
                }
            }
            
            // 检查字符串长度
            if (initConfig.maxlength !== undefined && value.toString().length > initConfig.maxlength) {
                const label = this.getInputLabel(element);
                return { isValid: false, message: `${label} 长度不能超过 ${initConfig.maxlength}` };
            }
            
            // 检查警告条件 - 适用于所有类型的字段
            if (initConfig.warning) {
                try {
                    const warningRegex = new RegExp(initConfig.warning.replace(/^\/|\/$/g, ''));
                    if (!warningRegex.test(value.toString())) {
                        const label = this.getInputLabel(element);
                        return { 
                            isValid: true, 
                            warning: true, 
                            message: initConfig.warningMessage || `${label} 格式不符合建议规范` 
                        };
                    }
                } catch (e) {
                    console.error('Error evaluating warning regex:', e);
                }
            }
            
            // 其他自定义验证规则...
            
        } catch (e) {
            console.error('Error parsing data-init:', e);
            return { isValid: true }; // 解析失败时默认为有效
        }
        
        return { isValid: true };
    }

    /**
     * 验证表单元素
     * @param element 表单元素
     * @param value 字段值
     * @param path 字段路径
     * @param errors 错误数组
     * @param warnings 警告数组
     */
    private validateElement(element: Element, value: any, path: string, errors: ValidationErrorDetail[], _warnings: ValidationWarningDetail[]): void {
        const result = this.validateFieldValue(element, value);
        if (!result.isValid && result.message) {
            errors.push({
                path,
                message: result.message
            });
        }
    }

    /**
     * 验证租赁详情
     * @param detail 租赁详情
     * @param index 租赁详情索引
     * @param errors 错误数组
     * @param warnings 警告数组
     * @param context 验证上下文
     */
    public validateLeaseDetail(detail: LeaseDetail, index: number, errors: ValidationErrorDetail[], warnings: ValidationWarningDetail[], context: { $el: HTMLElement, alpineComponent: any }): void {
        // 遍历所有字段
        this.traverseObject(detail, (value, path) => {
            const fullPath = `leaseDetails[${index}].${path}`;
            const element = this.findElementByPath(fullPath, context);
            
            if (element) {
                this.validateElement(element, value, fullPath, errors, warnings);
            }
        });
    }

    /**
     * 遍历对象的所有字段
     * @param obj 要遍历的对象
     * @param callback 回调函数，接收字段值和路径
     * @param prefix 路径前缀
     */
    private traverseObject(obj: any, callback: (value: any, path: string) => void, prefix: string = ''): void {
        if (!obj || typeof obj !== 'object') return;
        
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                const value = obj[key];
                const path = prefix ? `${prefix}.${key}` : key;
                
                if (Array.isArray(value)) {
                    // 处理数组
                    value.forEach((item, index) => {
                        if (typeof item === 'object' && item !== null) {
                            this.traverseObject(item, callback, `${path}[${index}]`);
                        } else {
                            callback(item, `${path}[${index}]`);
                        }
                    });
                } else if (typeof value === 'object' && value !== null) {
                    // 处理嵌套对象
                    this.traverseObject(value, callback, path);
                } else {
                    // 处理基本类型
                    callback(value, path);
                }
            }
        }
    }

    /**
     * 标记元素为警告状态并添加警告提示
     * @param element 要标记的元素
     * @param message 警告消息
     */
    public markElementAsWarning(element: Element, message: string = ''): void {
        const container = element.closest('.w-public-input');
        if (container) {
            container.classList.add('warning');
            // 如果有警告消息，添加到标签元素中
            if (message) {
                const label = container.querySelector('.w-form-warning');
                if (label) {
                    // 直接使用传入的 message 作为警告消息
                    label.textContent = ` ${message}`;
                }
            }
        }
    }

    /**
     * 清除所有验证标记
     * @param rootElement 根元素，默认为 document
     */
    public clearValidationMarks(rootElement: Element | Document = document): void {
        // 清除错误标记
        rootElement.querySelectorAll('.w-public-input.error').forEach(el => {
            el.classList.remove('error');
        });
        
        // 清除警告标记
        rootElement.querySelectorAll('.w-public-input.warning').forEach(el => {
            el.classList.remove('warning');
        });
        
        // 清除导航项的错误标记
        rootElement.querySelectorAll('.nav-item.error, li[data-target].error').forEach(el => {
            el.classList.remove('error');
        });
        
        // 清除导航项的警告标记
        rootElement.querySelectorAll('.nav-item.warning, li[data-target].warning').forEach(el => {
            el.classList.remove('warning');
        });
        
        // console.log('Cleared all validation marks');
    }

    /**
     * 评估条件表达式
     * @param condition 条件表达式
     * @param detail 数据对象
     * @returns 条件是否满足
     */
    private evaluateCondition(condition: string, detail: any): boolean {
        try {
            // 替换条件中的 currentLeaseDetail 为 detail
            const processedCondition = condition
                .replace(/currentLeaseDetail\./g, 'detail.');
            
            // 创建一个安全的评估环境
            const evalFunc = new Function('detail', `
                try {
                    return !!(${processedCondition});
                } catch (e) {
                    console.error('Error evaluating condition:', e);
                    return false;
                }
            `);
            
            return evalFunc(detail);
        } catch (e) {
            console.error('Failed to create evaluation function:', e);
            return false;
        }
    }
} 