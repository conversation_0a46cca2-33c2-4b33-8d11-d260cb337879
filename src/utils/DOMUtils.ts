/**
 * DOMUtils - 管理与 DOM 操作相关的实用函数
 */
export class DOMUtils {
    /**
     * 滚动到指定的元素
     * @param selectorOrEl 可以是元素的选择器字符串或 HTMLElement 实例
     * @param scrollOptions 可选的滚动参数，包含 behavior 和 block 属性
     * 此函数根据传入的参数判断是使用选择器查找元素还是直接操作传入的元素
     * 如果传入的是选择器字符串，使用 document.querySelector 查找元素
     * 如果传入的是 HTMLElement 实例，直接使用该元素进行滚动
     * 如果未找到元素或传入的元素无效，会在控制台输出警告信息并返回
     * 如果找到元素，将使用 scrollIntoView 方法将元素滚动到视口中央，滚动行为为平滑滚动
     */
    static scrollToElement(selectorOrEl: string | HTMLElement, scrollOptions?: ScrollIntoViewOptions): void {
        let element: HTMLElement | null = null;

        // 判断传入的参数类型
        if (typeof selectorOrEl === 'string') {
            element = document.querySelector(selectorOrEl);
        } else if (selectorOrEl instanceof HTMLElement) {
            element = selectorOrEl;
        }

        if (!element) {
            console.warn(`Element not found or invalid:`, selectorOrEl);
            return;
        }

        // 使用传入的 scrollOptions，如果没有传入则使用默认值
        element.scrollIntoView(scrollOptions || { behavior: "smooth", block: "center" });
    }

    /**
     * 根据条件生成 HTML
     * @param condition 判断条件，当为 true 时，将内容包裹在 <span> 标签中，否则返回空字符串
     * @param content 要包裹在 <span> 标签中的内容
     * 此函数接收一个布尔类型的条件和一个字符串类型的内容作为参数
     * 若条件为真，将内容用 <span> 标签包裹并返回，若条件为假，返回空字符串
     */
    static generateHtml(condition: boolean, content: string): string {
        return condition? `<span>${content}</span>` : '';
    }

    /**
     * 解析 data-init 配置
     * @param dataInitContent 要解析的 data-init 内容
     * @returns 解析后的配置对象
     */
    static parseDataInit(dataInitContent: string): any {
        try {
            // 处理模板字符串的情况
            if (dataInitContent.startsWith('`') && dataInitContent.endsWith('`')) {
                // 移除反引号
                dataInitContent = dataInitContent.slice(1, -1);
                // 处理模板字符串中的转义字符
                dataInitContent = dataInitContent.replace(/\\\\d/g, '\\d')
                                              .replace(/\\\\/g, '\\');
            }

            // 处理普通的 JSON 字符串
            const escapedContent = dataInitContent.replace(/([{,])\s*([\w$]+)\s*:/g, '$1"$2":')
                                                .replace(/'([^']*)'/g, '"$1"')
                                                .replace(/\\/g, '\\\\');
            
            return JSON.parse(escapedContent);
        } catch (e) {
            console.warn('Failed to parse data-init config:', dataInitContent, e);
            return {};
        }
    }
    
    static formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

}