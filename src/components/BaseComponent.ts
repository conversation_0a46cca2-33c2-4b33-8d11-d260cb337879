import Alpine from 'alpinejs';
import { DOMUtils } from '../utils/DOMUtils';
import Inputmask from 'inputmask';
import { Validator } from '../utils/Validator';
export abstract class BaseComponent {
    protected $el: HTMLElement;
    // 添加 validator 属性
    protected validator: Validator =  new Validator();

    constructor() {
        // 子类需要初始化 $el
        this.$el = document.getElementById('financialSystemComponent') as HTMLElement;
    }

    public init() {
        // Ensure $el is set to the root element of the component
        this.bindAnchorEvents();
    }

    /**
     * 绑定锚点点击事件
     */
    private bindAnchorEvents(): void {
        const anchorList = this.$el.querySelectorAll('.aimingPoint ul li');
        anchorList.forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.stopPropagation();
                const target = e.currentTarget as HTMLElement;
                this.scrollToTarget(target);
            });
        });
        // 添加滚动监听
        this.bindScrollEvents();
    }

   

     

    /**
     * 滚动到目标位置
     * @param anchorElement 锚点元素
     */
    protected scrollToTarget(anchorElement: HTMLElement): void {
        // 移除其他li的active类
        const allAnchors = this.$el.querySelectorAll('.aimingPoint ul li');
        allAnchors.forEach(a => a.classList.remove('active'));

        // 添加当前点击的li的active类
        anchorElement.classList.add('active');

        // 获取目标元素的id
        const targetId = anchorElement.getAttribute('data-target');
        if (!targetId) return;

        // 获取目标元素
        const targetElement = document.getElementById(targetId);
        if (!targetElement) return;

        // 使用 DOMUtils 滚动到目标位置
        DOMUtils.scrollToElement(targetElement, {
            behavior: 'smooth',
            block: 'start'
        });
    }

     /**
     * 绑定滚动事件，监听 po-child 元素的可见性
     */
     private bindScrollEvents(): void {
        // 获取所有的 po-child 元素和对应的 pointResult 容器
        const buContainer = this.$el.querySelector('#buContainer');
        const leaseContainer = this.$el.querySelector('#leaseContainer');
        
        if (!buContainer || !leaseContainer) return;

        // 为每个容器分别设置滚动监听
        const setupScrollListener = (container: Element) => {
            const sections = container.querySelectorAll('.po-child');
            const navItems = container.querySelector('.aimingPoint')?.querySelectorAll('ul li');
            const scrollContainer = container.querySelector('.pointResult');
            
            if (!sections.length || !navItems?.length || !scrollContainer) return;

            let currentSection: string | null = null;
            let scrollTimeout: number | null = null;

            // 使用节流的滚动事件处理
            scrollContainer.addEventListener('scroll', () => {
                // 如果已经有待处理的更新，取消它
                if (scrollTimeout) {
                    window.clearTimeout(scrollTimeout);
                }

                // 设置新的延迟更新
                scrollTimeout = window.setTimeout(() => {
                    // 确保当前容器是可见的
                    if ((container as HTMLElement).style.display === 'none') return;

                    let maxVisibility = 0;
                    let mostVisibleSection: string | null = null;

                    sections.forEach(section => {
                        const rect = section.getBoundingClientRect();
                        const containerRect = scrollContainer.getBoundingClientRect();
                        
                        // 计算元素在视口中的位置
                        const visibleTop = Math.max(0, rect.top - containerRect.top);
                        const visibleBottom = Math.min(containerRect.height, rect.bottom - containerRect.top);
                        const visibleHeight = Math.max(0, visibleBottom - visibleTop);
                        const visibility = visibleHeight / rect.height;

                        // 调整可见度计算逻辑
                        if (visibility > maxVisibility && visibility > 0.2) { // 降低阈值到20%
                            maxVisibility = visibility;
                            mostVisibleSection = section.id;
                        }
                    });

                    // 如果找到最可见的部分，且与当前不同，则更新导航
                    if (mostVisibleSection && mostVisibleSection !== currentSection) {
                        currentSection = mostVisibleSection;
                        
                        // 更新导航项的激活状态
                        navItems.forEach(item => {
                            const targetId = item.getAttribute('data-target');
                            if (targetId === mostVisibleSection) {
                                item.classList.add('active');
                            } else {
                                item.classList.remove('active');
                            }
                        });
                    }
                }, 100);
            }, { passive: true });

            // 初始触发一次滚动事件，设置初始状态
            scrollContainer.dispatchEvent(new Event('scroll'));
        };

        // 为两个容器分别设置滚动监听
        setupScrollListener(buContainer);
        setupScrollListener(leaseContainer);
    }

    /**
     * 处理输入变化的事件处理函数
     */
    private handleInputChange = (event: Event) => {
        this.validateOnChange(event.target as HTMLElement);
    }

    /**
     * 初始化日历组件和输入框限制
     */
    protected initInputDateMask(container: string | HTMLElement): void {
        Alpine.nextTick(() => {
            let containerElement: HTMLElement | null;

            if (typeof container === 'string') {
                containerElement = document.getElementById(container);
                if (!containerElement) {
                    console.error(`Container with ID ${container} not found.`);
                    return;
                }
            } else {
                containerElement = container;
            }

            const inputs = containerElement.querySelectorAll('input');
            inputs.forEach(input => {
                const dataInitContent = input.getAttribute('data-init');
                if (dataInitContent) {
                    try {
                        const config = DOMUtils.parseDataInit(dataInitContent);
                        if (config.dateTime) {
                            this.initializeDateTimePicker(input, config);
                        } else {
                            this.initInputMask(input, config);
                        }
                    } catch (e) {
                        console.error('Failed to parse data-init config:', e);
                    }
                }
                
                // 先移除之前的监听器以避免重复
                input.removeEventListener('change', this.handleInputChange);
                input.addEventListener('change', this.handleInputChange);
                
                // 对于日期和数字输入，也监听 blur 事件
                if (input.classList.contains('w-form-control-date') || 
                    input.getAttribute('data-init')?.includes('numeric') || 
                    input.getAttribute('data-init')?.includes('currency') || 
                    input.getAttribute('data-init')?.includes('decimal')) {
                    input.removeEventListener('blur', this.handleInputChange);
                    input.addEventListener('blur', this.handleInputChange);
                }
            });

            containerElement.querySelectorAll('select').forEach(select => {
                const xModel = select.getAttribute('x-model');
                const xBindDisabled = select.getAttribute('x-bind:disabled');
                
                if (xModel) {
                    // 初始化 selectpicker
                    // @ts-ignore
                    // 初始化 selectpicker，添加关键配置
                    $(select).selectpicker({
                        mobile: false
                    });
                
                    // 监听数据变化和禁用状态
                    Alpine.effect(() => {
                        const currentValue = Alpine.evaluate(select, xModel);
                        // 同时监听 disabled 状态
                        if (xBindDisabled) {
                            Alpine.evaluate(select, xBindDisabled);
                        }
                        
                        setTimeout(() => {
                            if (!currentValue && select.options.length > 0) {
                                const firstValue = select.options[0].value;
                                Alpine.evaluate(select, `${xModel} = '${firstValue}'`);
                            }
                            // @ts-ignore
                            //$(select).selectpicker('val', currentValue);
                            // @ts-ignore
                            $(select).selectpicker('refresh');
                        }, 0);
                    });
                    
                    // 添加 change 事件监听器，用于实时验证
                    select.removeEventListener('change', this.handleInputChange);
                    select.addEventListener('change', this.handleInputChange);
                }
            });
        })
    }

    /**
     * 初始化日历
     */
    protected initializeDateTimePicker(input: HTMLInputElement, config: any): void {
        // @ts-ignore
        const $input = $(input);
        let option: any = {
            format: "yyyy-mm-dd",
            autoclose: true,
            todayBtn: true,
            language: 'zh-CN',
            forceParse: true,
            minView: 2,
        }

        if (config.endDateEl) {
            // @ts-ignore
            const endDateEl = $('#' + config.endDateEl);
            const endDate = endDateEl.val();
            option.endDate = endDate;
        } else if (config.startDateEl) {
            // @ts-ignore
            const startDateEl = $('#' + config.startDateEl);
            const startDate = startDateEl.val();
            option.startDate = startDate;
        }

        $input.datetimepicker(option);
        
        // 统一的日期处理函数
        const handleDateUpdate = (date: Date) => {
            const formattedDate = DOMUtils.formatDate(date);
            
            // 更新输入值
            $input.val(formattedDate);
            
            // 更新 Alpine.js 数据绑定
            const xModel = input.getAttribute('x-model');
            if (xModel && this.$el) {
                Alpine.evaluate(input, `${xModel} = '${formattedDate}'`);
            }

            // 处理关联日期控件
            if (config.endDateEl) {
                // @ts-ignore
                $('#' + config.endDateEl).datetimepicker('setStartDate', date);
            }
            if (config.startDateEl) {
                // @ts-ignore
                $('#' + config.startDateEl).datetimepicker('setEndDate', date);
            }
        };

        // 格式化输入的日期文本
        const formatDateInput = () => {
            const inputValue = $input.val() as string;
            if (!inputValue || inputValue.trim() === '') return;

            try {
                // 使用新的日期格式化方法
                const formattedDate = this.tryFormatDateString(inputValue);
                if (formattedDate) {
                    // 如果格式化成功，创建日期对象并更新
                    const date = new Date(formattedDate);
                    if (!isNaN(date.getTime())) {
                        handleDateUpdate(date);
                    }
                }
            } catch (e) {
                console.warn('Invalid date format:', inputValue, e);
            }
        };
        
        // 监听各种事件
        $input
            .on('blur', formatDateInput)
            .on('paste', () => setTimeout(formatDateInput, 100))
            .on('changeDate', (e: any) => handleDateUpdate(e.date))
            .on('keydown', (e: any) => {
                // 添加回车键格式化功能
                if (e.key === 'Enter' || e.keyCode === 13) {
                    e.preventDefault();
                    formatDateInput();
                    
                    // 如果需要，可以在这里添加额外的逻辑
                    
                    // 保持焦点在输入框上
                    setTimeout(() => {
                        input.focus();
                    }, 10);
                }
            });
    }

    /**
     * 初始化输入框的输入限制
     */
    protected initInputMask(el: HTMLElement, config: any): void {
        if (!(el as any).inputmask) {
            try {
                const maskConfig = {
                    ...config,
                    oncomplete: (event: any) => {
                        const value = event.target.inputmask.unmaskedvalue();
                        
                        const xModel = el.getAttribute('x-model');
                        if (xModel) {
                            Alpine.evaluate(el, `${xModel} = '${value}'`);
                        }
                    },
                };
                
                Inputmask(maskConfig).mask(el);
            } catch (error) {
                console.error('Invalid Inputmask config:', error);
            }
        }
    }

    /**
     * 清除元素上的计算效果
     * @param container 容器元素
     */
    protected clearElementComputeEffects(container: Element | null): void {
        if (!container) return;
        
        // 清理日历和输入掩码组件
        const formElements = container.querySelectorAll('input, select, textarea');
        //console.log(`Found ${formElements.length} form elements to clean`);
        
        formElements.forEach(el => {
            // 销毁日历组件
            if (el.classList.contains('w-form-control-date')) {
                const datepicker = (el as any).datepicker;
                if (datepicker && typeof datepicker.destroy === 'function') {
                    try {
                         // 使用 jQuery 方式销毁 datetimepicker
                        // @ts-ignore
                        $(el).datetimepicker('remove');
                    } catch (e) {
                        console.error('Error destroying datepicker:', e);
                    }
                }
            }
            
            // 销毁输入掩码
            if ((el as any).inputmask) {
                try {
                    (el as any).inputmask.remove();
                } catch (e) {
                    console.error('Error removing inputmask:', e);
                }
            }
            
            // 销毁 selectpicker
            if (el.classList.contains('selectpicker') || el.classList.contains('w-form-select')) {
                try {
                    // 如果使用了 Bootstrap-select 插件
                    // @ts-ignore
                    if (typeof $(el).selectpicker === 'function') {
                        // @ts-ignore
                        $(el).selectpicker('destroy');
                    }
                } catch (e) {
                    console.error('Error destroying selectpicker:', e);
                }
            }
        });
        
        //console.log('Finished cleaning up compute effects');
    }

    /**
     * 滚动到指定元素
     * @param element 要滚动到的元素
     */
    protected scrollToElement(element: HTMLElement): void {
        DOMUtils.scrollToElement(element, { behavior: 'smooth', block: 'center' });
    }


    /**
     * 标记元素为错误状态
     * @param element 要标记的元素
     */
    protected markElementAsError(element: Element): void {
        const container = element.closest('.w-public-input');
        if (container) {
            container.classList.add('error');
        }
    }

    /**
     * 标记元素为警告状态
     * @param element 要标记的元素
     */
    protected markElementAsWarning(element: Element): void {
        const container = element.closest('.w-public-input');
        if (container) {
            container.classList.add('warning');
        }
    }


     /**
     * 显示提示消息
     * @param message 消息内容
     * @param type 消息类型：'success', 'error', 'warning'
     */
     protected showToast(message: string, type: 'success' | 'error' | 'warning'): void {
        // 如果你有现成的 toast 组件，可以直接调用
        // 例如：Toast.show(message, type);
        
        // 如果没有，可以创建一个简单的 toast 元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // 2秒后自动移除
        setTimeout(() => {
            toast.classList.add('toast-hide');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }

    /**
     * 展开包含错误元素的容器并添加警告图标
     * @param element 错误元素
     */
    protected expandErrorContainer(element: Element): void {
        // 找到错误元素所在的 po-child 容器
        const section = element.closest('.po-child');
        if (section) {
            // 展开容器
            section.classList.add('open');
            
            // 更新展开/收起按钮文本
            const headerLink = section.querySelector('.po-child-header .put-right');
            if (headerLink) {
                headerLink.textContent = '收起';
            }
            
            // 获取容器的 ID，用于添加导航警告图标
            const sectionId = section.id;
            if (sectionId) {
                this.addWarningIcon(sectionId);
            }
        }
    }

    /**
     * 为指定 ID 的导航项添加警告图标
     * @param sectionId 区域 ID
     */
    protected addWarningIcon(sectionId: string): void {
        // 查找对应的导航项
        const navItem = document.querySelector(`[data-target="${sectionId}"]`);
        if (navItem) {
            // 添加错误样式
            navItem.classList.add('error');
        }
    }

     /**
     * 展开并滚动到 Lease 部分
     */
     protected expandAndScrollToLeaseSection(): void {
        const leaseSection = document.getElementById('lease') as HTMLElement;
        if (leaseSection) {
            leaseSection.classList.add('open');
            const headerLink = leaseSection.querySelector('.po-child-header .put-right');
            if (headerLink) {
                headerLink.textContent = '收起';
            }
            DOMUtils.scrollToElement(leaseSection);
        }
    }

 

    /**
     * 在元素值变化时进行验证
     * @param element 要验证的元素
     */
    protected validateOnChange(element: HTMLElement): void {
        // 清除当前元素的错误和警告标记
        const container = element.closest('.w-public-input');
        if (container) {
            container.classList.remove('error');
            container.classList.remove('warning');

            // 清除警告消息
            const warningLabel = container.querySelector('.w-form-warning');
            if (warningLabel) {
                warningLabel.textContent = '';
            }
        }

        // 获取元素的 x-model 属性
        const xModel = element.getAttribute('x-model');
        if (!xModel) return;

        // 获取元素的值
        let value = Alpine.evaluate(element, xModel);

        // 如果是日期输入框，尝试格式化日期
        if (element.classList.contains('w-form-control-date')) {
            const formattedDate = this.tryFormatDateString(value as string);
            if (formattedDate && formattedDate !== value) {
                // 更新值到 Alpine.js 数据绑定
                Alpine.evaluate(element, `${xModel} = '${formattedDate}'`);
                // 更新输入框显示值
                if (element instanceof HTMLInputElement) {
                    element.value = formattedDate;
                }
                value = formattedDate;
            }
        }

        // 验证元素值
        const result = this.validator.validateFieldValue(element, value);

        if (!result.isValid) {
            // 使用 validator 的方法标记错误
            this.validator.markElementAsError(element);
        } else if (result.warning) {
            // 使用 validator 的方法标记警告
            this.validator.markElementAsWarning(element, result.message);
        }
    }

    /**
     * 尝试将字符串格式化为标准日期格式 (YYYY-MM-DD)
     * @param dateString 输入的日期字符串
     * @returns 格式化后的日期字符串，如果无法格式化则返回 null
     */
    protected tryFormatDateString(dateString: string): string | null {
        if (!dateString || typeof dateString !== 'string') return null;

        const trimmedValue = dateString.trim();
        if (!trimmedValue) return null;

        // 如果已经是标准格式，直接返回
        if (/^\d{4}-\d{2}-\d{2}$/.test(trimmedValue)) {
            // 验证日期是否有效
            const date = new Date(trimmedValue);
            if (!isNaN(date.getTime())) {
                return trimmedValue;
            }
        }

        try {
            let date: Date | null = null;

            // 处理各种可能的日期格式

            // 1. 处理 YY-MM-DD 格式 (如 26-03-26)
            const shortYearMatch = trimmedValue.match(/^(\d{2})-(\d{1,2})-(\d{1,2})$/);
            if (shortYearMatch) {
                let [_, yearStr, monthStr, dayStr] = shortYearMatch;
                let year = parseInt(yearStr, 10);
                const month = parseInt(monthStr, 10);
                const day = parseInt(dayStr, 10);

                // 确保两位数年份都被解释为21世纪 (2000-2099)
                if (year < 100) {
                    year += 2000;
                }

                // 验证月份和日期的有效性
                if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                    date = new Date(year, month - 1, day);
                }
            }

            // 2. 处理 YYYY-M-D 格式 (如 2026-3-6)
            else if (/^\d{4}-\d{1,2}-\d{1,2}$/.test(trimmedValue)) {
                const parts = trimmedValue.split('-');
                const year = parseInt(parts[0], 10);
                const month = parseInt(parts[1], 10);
                const day = parseInt(parts[2], 10);

                if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                    date = new Date(year, month - 1, day);
                }
            }

            // 3. 处理其他可能的分隔符格式 (如 26/03/26, 26.03.26)
            else {
                const separatorMatch = trimmedValue.match(/^(\d{1,4})[\/\.](\d{1,2})[\/\.](\d{1,4})$/);
                if (separatorMatch) {
                    let [_, part1, part2, part3] = separatorMatch;
                    let year: number, month: number, day: number;

                    // 判断哪一部分是年份
                    if (part1.length === 4) {
                        // YYYY/MM/DD 格式
                        year = parseInt(part1, 10);
                        month = parseInt(part2, 10);
                        day = parseInt(part3, 10);
                    } else if (part3.length === 4) {
                        // DD/MM/YYYY 格式
                        day = parseInt(part1, 10);
                        month = parseInt(part2, 10);
                        year = parseInt(part3, 10);
                    } else {
                        // YY/MM/DD 格式，假设第一部分是年份
                        year = parseInt(part1, 10);
                        month = parseInt(part2, 10);
                        day = parseInt(part3, 10);

                        if (year < 100) {
                            year += 2000;
                        }
                    }

                    if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                        date = new Date(year, month - 1, day);
                    }
                }
            }

            // 如果成功创建了有效日期，返回格式化的字符串
            if (date && !isNaN(date.getTime())) {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

        } catch (e) {
            console.warn('Error formatting date string:', trimmedValue, e);
        }

        return null;
    }

    /**
     * 更新导航标签的错误或警告状态
     * @param xModel x-model 属性值
     * @param type 标记类型：'error' 或 'warning'
     */
    protected updateNavErrorStatus(xModel: string, type: 'error' | 'warning'): void {
        // 根据 x-model 确定应该标记哪个导航项
        let targetNav = '';

        if (xModel.includes('data.finance.')) {
            // BU 级别的字段
            if (xModel.includes('buCode') || xModel.includes('buName') || xModel.includes('finMarket')) {
                targetNav = 'basic-info';
            } else if (xModel.includes('signDate') || xModel.includes('openDate')) {
                targetNav = 'bu-level';
            } else if (xModel.includes('addInfo')) {
                targetNav = 'additional-info';
            }
        } else if (xModel.includes('currentLeaseDetail.')) {
            // Lease 级别的字段
            if (xModel.includes('lease.')) {
                targetNav = 'LeaseDetail_lease';
            } else if (xModel.includes('rb.')) {
                targetNav = 'LeaseDetail_br';
            } else if (xModel.includes('sov.')) {
                targetNav = 'LeaseDetail_sov';
            }
        }

        // 标记对应的导航项
        if (targetNav) {
            const navItem = document.querySelector(`li[data-target="${targetNav}"]`);
            if (navItem) {
                navItem.classList.add(type);
            }
        }
    }
}