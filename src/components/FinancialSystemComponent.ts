import Alpine from 'alpinejs';
import { LeaseDetail, FinancialData,RbPhase, SovRule ,ValidationResult ,AlpineComponent} from '../types/financial';
import { BaseComponent } from './BaseComponent';
import { DataCleaner } from '../utils/DataCleaner';
import { DOMUtils } from '../utils/DOMUtils';


export default class FinancialSystemComponent extends BaseComponent {
    private buContainer: HTMLElement | null = null;
    private leaseContainer: HTMLElement | null = null;
    public currentLeaseDetail: LeaseDetail = DataCleaner.getDefaultLeaseDetail();
    // 添加验证结果作为类属性
    private validationResult: ValidationResult = {
        isValid: true,
        buErrors: [],
        leaseErrors: [],
        buWarnings: [],
        leaseWarnings: []
    };

    constructor(data: FinancialData | null) {
        super();
        const cleanedData = DataCleaner.cleanFinancialData(data || DataCleaner.getDefaultFinancialData());
        this.registerComponent(cleanedData);
        Alpine.start();
    }

    /**
     * Register Alpine component with data and methods
     */
    private registerComponent(data: FinancialData): void {
        const _this = this;
        Alpine.data('financialSystemComponent', () => ({
            data,
            isBuVisible: true,//显示bu 信息
            isLeaseVisible: false,//显示租赁信息
            currentLeaseDetail: DataCleaner.getDefaultLeaseDetail(),//当前租赁信息
            indexLeaseDetail: -1,//当前租赁信息索引
            invalidDetails: [] as number[],//验证时候保存，无效的租赁信息索引
            warningDetails: [] as Array<{ index: number; message: string[] }>,//警告信息
            /**
             * 绑定容器下的输入遮罩和 时间控件
             * @param container 要初始化的容器
             */
            initInputDateMask(container: string | HTMLElement){
                _this.initInputDateMask(container);
            },
            /**
             * 检查指定索引是否有警告
             * @param index 要检查的索引
             * @returns 布尔值，表示指定索引是否有警告
             */
            checkWarning(index: number) {
                return this.warningDetails.some(detail => detail.index === index);
            },
            // 修改 watchLeaseTypeDE 方法，添加日志
            watchLeaseTypeDE() {
                const isD4 = this.data.finance.leaseTypeDE === 'D4';
                console.log('watchLeaseTypeDE executing, isD4:', isD4);
                
                // 更新所有 leaseDetails
                this.data.finance?.leaseDetails?.forEach(detail => {
                    if (detail.sov) {
                        console.log('Updating detail sov:', detail.sov.minMaxNeedYes, '->', isD4 ? 'Y' : 'N');
                        detail.sov.minMaxNeedYes = isD4 ? 'Y' : 'N';
                    }
                });
            },
            /**
             * UI 检查指定索引的 lease detail 是否有效
             * @param index 要检查的索引
             * @returns 布尔值，表示指定索引的 lease detail 是否有效
             */
            checkValid(index:number){
                const _bl=this.invalidDetails.includes(index);
                return _bl;
            },
            /**
             * BU 界面 获取用途选择文本
             * @param useOfLeaseNo 用途选择值
             * @returns 用途选择文本
             */
            getUseOfLeaseNoText(useOfLeaseNo: string) {
                const select = document.getElementById('useOfLeaseNoMapping') as HTMLSelectElement;
                if (!select) {
                    console.warn('Element with ID "useOfLeaseNoMapping" not found.');
                    return useOfLeaseNo; // Return the useOfLeaseNo as a fallback
                }
                const option = Array.from(select.options).find(opt => opt.value === useOfLeaseNo);
                return option ? option.text : useOfLeaseNo;
            },
            /**
             * UI操作切换阶段内容
             * @param event 事件对象
             */
            toggleContent(event: Event) {
                event.stopPropagation();
                const header = event.currentTarget as HTMLElement;
                const section = header.closest('.po-child');
                if (section) {
                    section.classList.toggle('open');
                    const link = header.querySelector('.put-right');
                    if (link) {
                        link.textContent = section.classList.contains('open') ? '收起' : '展开';
                    }
                }
            },
            /**
             * UI 操作 切换阶段内容
             * @param event 事件对象
             */
            togglePhaseContent(event: Event) {
                event.stopPropagation();
                // 获取当前点击的元素
                const currentElement = event.currentTarget as HTMLElement;
                // 找到最近的 <li> 元素
                const listItem = currentElement.closest('li') as HTMLElement;
                // 如果找到 <li> 元素，切换 open 类
                if (listItem) {
                    listItem.classList.toggle('open');
                    if (currentElement) {
                        currentElement.textContent = listItem.classList.contains('open') ? '收起' : '展开';
                    }
                }
            },
            init() {
                _this.init();
                _this.buContainer = document.getElementById('buContainer');
                _this.leaseContainer = document.getElementById('leaseContainer');
                this.showBuView();
            },
            //显示bu 信息
            showBuView(){
                // 确保在切换视图之前清理所有计算效果
                _this.clearElementComputeEffects(_this.leaseContainer);
               
                // 滚动到 leaseContainer 下的 pointResult 元素顶部
                if (_this.leaseContainer) {
                    const pointResult = _this.leaseContainer.querySelector('.pointResult.hasblack-tip');
                    if (pointResult) {
                        // 设置 leaseContainer 的滚动位置为0
                        pointResult.scrollTop = 0;
                        // 手动触发一次滚动事件
                        pointResult.dispatchEvent(new Event('scroll'));
                    }
                }   
                this.isBuVisible = true;
                this.isLeaseVisible = false;
                this.$nextTick(() => {
                    if (_this.buContainer) {
                        _this.initInputDateMask(_this.buContainer);

                        // 处理 BU 视图的验证错误
                        _this.handleValidationForCurrentView();
                    }  
                })
            },
            /**
             * 显示租赁信息
             * @param index 要显示的详细信息的索引
             * @param func 要执行的函数
             */
            showDetails(index: number, func?: Function){
                if (index >= 0 && index < this.data.finance!.leaseDetails!.length) {
                    // 直接引用原始数据，而不是创建深拷贝
                    this.currentLeaseDetail = this.data.finance!.leaseDetails![index];
                    this.indexLeaseDetail = index;
                    
                    // 清理所有监听器
                    _this.clearElementComputeEffects(_this.buContainer);

                    this.$nextTick(() => {
                        // 重置 Alpine 状态
                        this.isBuVisible = false;
                        this.isLeaseVisible = true;
                        
                        // 初始化输入掩码
                        _this.initInputDateMask('leaseContainer');

                        // 处理 Lease 视图的验证错误
                        _this.handleValidationForCurrentView();
                        // 执行回调
                        func?.call(_this);
                    });
                }  
            },
            /**
             * 处理用途选择变化
             * @param detail 要处理的 LeaseDetail 对象
             */
            handleUseOfLeaseNoChange(detail: LeaseDetail) {
                //选着用途后清空数据
                console.log(`useOfLeaseNo changed to ${detail.lease?.useOfLeaseNo}`);
                if (detail.rb) {
                    detail.rb.rbtrRent = '';
                    detail.rb.rbtrMgmt = '';
                    detail.rb.rbtrAdv = '';
                    detail.rb.rbtrFee = '';
                    detail.rb.rbFeeDesc = '';
                }
                // Reset or modify phases data
                detail.rb?.phases?.forEach((phase: RbPhase) => {
                    phase.rbAccruedAmountRent = '';
                    phase.rbAccruedAmountMgmt = '';
                    phase.rbAccruedAmountAdv = '';
                    phase.rbAccruedAmountFee = '';
                    phase.rbPaymentAmountRent = '';
                    phase.rbPaymentAmountMgmt = '';
                    phase.rbPaymentAmountAdv = '';
                    phase.rbPaymentAmountFee = '';
                    // Add more fields to reset as needed
                });
            },
            /**
             * BU界面上添加详 leaseDetail细信息
             */
            addDetail() {
                console.log("Adding detail");
                const newDetail = DataCleaner.getDefaultLeaseDetail();
                if (newDetail.sov && this.data.finance.leaseTypeDE === 'D4') {
                    newDetail.sov.minMaxNeedYes = 'Y';
                    newDetail.sov.minMaxType1 = "RENP";
                }
                // 添加到列表
                this.data.finance?.leaseDetails?.push(newDetail);
            },
            /**
             * BU界面上删除详细信息
             * @param index 要删除的详细信息的索引
             * @param asyncValidator 异步验证函数，返回是否可以删除
             */
            async deleteLease(index: number, asyncValidator?: (objectId?: string) => Promise<boolean>) {
                console.log("Deleting lease");
                
                try {
                    const leaseDetail = this.data.finance?.leaseDetails?.[index];
                    if (!leaseDetail) {
                        console.warn("Lease detail not found at index:", index);
                        return;
                    }

                    // 如果有异步验证函数，先执行验证
                    if (asyncValidator) {
                        const canDelete = await asyncValidator(leaseDetail.lease?.objectId);
                        if (!canDelete) {
                            console.log("Delete operation cancelled by validator");
                return;
            }
                    }

                    // 执行删除操作
                    this.data.finance?.leaseDetails?.splice(index, 1);
                    if (this.invalidDetails.includes(index)) {
                        this.invalidDetails = this.invalidDetails.filter(id => id !== index);
                    }
                    
                } catch (error) {
                    console.error('Error during lease deletion:', error);
                    // 可以在这里添加错误处理逻辑
                }
            },
            /**
             * 保存详细信息
             * @param index 要保存的详细信息的索引
             * @param asyncSaver 异步保存函数
             */
            async saveDetail(index: number, asyncSaver?: (detail: LeaseDetail) => Promise<LeaseDetail | null>) {
                console.log("Saving detail");
                
                try {
                    const detail = this.data.finance?.leaseDetails?.[index];
                    if (!detail) {
                        console.warn("Detail not found at index:", index);
                        return;
                    }

                    // 验证必填字段
                    let isValid = true;
                    const errorFields = new Set<string>();

                    // 验证 leaseCode
                    if (!detail.lease?.leaseCode?.trim()) {
                        isValid = false;
                        errorFields.add('leaseCode');
                    }

                    // 验证 useOfLeaseNo
                    if (!detail.lease?.useOfLeaseNo?.trim()) {
                        isValid = false;
                        errorFields.add('useOfLeaseNo');
                    }

                    // 更新错误样式和 invalidDetails
                    if (!isValid) {
                        // 添加到 invalidDetails 用于显示第一列的错误图标
                        if (!this.invalidDetails.includes(index)) {
                            this.invalidDetails.push(index);
                        }

                        // 为错误字段添加错误样式
                        errorFields.forEach(field => {
                            const td = document.getElementById(`lease-${field}-${index}`);
                            if (td) {
                                td.classList.add('error');
                            }
                        });

                        console.warn("Validation failed");
                        return;
                    } else {
                        // 验证通过，清除错误状态
                        this.invalidDetails = this.invalidDetails.filter(id => id !== index);
                        ['leaseCode', 'useOfLeaseNo'].forEach(field => {
                            const td = document.getElementById(`lease-${field}-${index}`);
                            if (td) {
                                td.classList.remove('error');
                            }
                        });
                    }

                    // 如果有异步保存函数，执行保存
                    if (asyncSaver) {
                        const savedDetail = await asyncSaver(detail);
                        if (savedDetail) {
                            // 更新数据
                            this.data.finance!.leaseDetails![index] = DataCleaner.cleanLeaseDetails([savedDetail])[0];
                            console.log("Detail saved successfully");
                        }
                    }

                } catch (error) {
                    console.error('Error saving detail:', error);
                }
            },
            addRbPhase() {
                console.log("Adding RB Phase");
                const newPhase = DataCleaner.getDefaultRbPhase();
                this.currentLeaseDetail?.rb?.phases?.push(newPhase);        
                this.$nextTick(() => {
                    // 更精确地定位新添加的 RB Phase 元素
                    const rbPhasesContainer = document.querySelector('#LeaseDetail_br .po-child-content-hasPhase ul');
                    if (rbPhasesContainer) {
                        const newPhaseElement = rbPhasesContainer.lastElementChild;
                        if (newPhaseElement) {
                            DOMUtils.scrollToElement(newPhaseElement as HTMLElement, { behavior: "smooth", block: "start" });
                }
            }
        });
            },
            deleteRbPhase(index: number) {
                // 找到要删除的 phase 元素
                const phaseElement = document.querySelector(`#LeaseDetail_br .po-child-content-hasPhase ul li:nth-child(${index + 1})`);
                // 清除该元素的计算效果
                if (phaseElement) {
                    _this.clearElementComputeEffects(phaseElement);
                }
                // 删除数据
                this.currentLeaseDetail?.rb?.phases?.splice(index, 1);
            },
            /**
             * 添加 SOV Phase阶段
             */
            addSovPhase() {
                const newPhase = DataCleaner.getDefaultSovRule();
                this.currentLeaseDetail?.sov?.sovRules?.push(newPhase);
                this.$nextTick(() => { 
                            // 更精确地定位新添加的 SOV Phase 元素
                            const sovPhasesContainer = document.querySelector('#LeaseDetail_sov .po-child-content-hasPhase ul');
                            if (sovPhasesContainer) {
                                const newPhaseElement = sovPhasesContainer.lastElementChild;
                                if (newPhaseElement) {
                                    DOMUtils.scrollToElement(newPhaseElement as HTMLElement,{ behavior: "smooth", block: "start" });
                        }
                    }
                });
            },
            /**
             * 删除 SOV Phase阶段
             * @param index 要删除的 phase 的索引
             */
            deleteSovPhase(index: number) {
                // 找到要删除的 phase 元素
                const phaseElement = document.querySelector(`#LeaseDetail_sov .po-child-content-hasPhase ul li:nth-child(${index + 1})`);
                
                // 清除该元素的计算效果
                if (phaseElement) {
                    _this.clearElementComputeEffects(phaseElement);
                }
                
                // 删除数据
                this.currentLeaseDetail?.sov?.sovRules?.splice(index, 1);
            },
            
            /**
             * leaseDetail 界面 SOV 界面  比例及档位

             * @param rule 要删除的规则
             * @param index 要删除的规则的索引
             */
            removeSovRuleList(rule: SovRule, index: number) {
                // 找到要删除的规则元素
                const ruleElement = document.querySelector(`#LeaseDetail_sov .sov-rule-list:nth-child(${index + 1})`);
                
                // 清除该元素的计算效果
                if (ruleElement) {
                    _this.clearElementComputeEffects(ruleElement);
                }
                
                // 删除数据
                rule.sovRuleList?.splice(index, 1);
            },
            /**
             * leaseDetail 界面 SOV 界面 比例及档位
             * 添加 sovRuleList
             * @param ruleIndex 要添加到的规则索引，如果不提供则添加到当前选中的规则
             */
             addSovRuleList(ruleIndex?: number) {
                if (this.currentLeaseDetail?.sov?.sovRules) {
                    // 如果提供了特定索引，只向该索引的规则添加
                    if (ruleIndex !== undefined && ruleIndex >= 0 && ruleIndex < this.currentLeaseDetail.sov.sovRules.length) {
                        const rule = this.currentLeaseDetail.sov.sovRules[ruleIndex];
                        if (rule && rule.sovRuleList) {
                            rule.sovRuleList.push({
                                objectId: '',
                                sovRuleId: '',
                                rateNum: 0,
                                breakpointYes: '',
                                breakpoint: '',
                                sovPayPercentage: '',
                                sovAccPercentage: ''
                            });
                        }
                    }
                }
            },
            goBack(){
                // 不需要显式保存，因为数据已经直接修改了原始对象
                this.showBuView();
            },        
            /**
             * 处理RB支付频率变化，重新计算所有付款金额
             * @param event 事件对象
             * @param calculateFnName 计算函数名称
             */
            handleRbPaymentFrequencyChange(_event: Event, calculateFnName: string = 'calculateTotal') {
                // 获取当前支付频率
                const frequency = this.currentLeaseDetail?.rb?.rbPaymentFrequency;
                console.log('Payment frequency changed to:', frequency);
                
                // 如果没有phases，直接返回
                if (!this.currentLeaseDetail?.rb?.phases || this.currentLeaseDetail?.rb?.phases?.length === 0) {
                    return;
                }
                
                // 获取计算函数
                // @ts-ignore
                const calculateFn = window[calculateFnName];
                if (typeof calculateFn !== 'function') {
                    console.error(`Function ${calculateFnName} is not defined`);
                    return;
                }
                
                // 获取当前的 useOfLeaseNo 值
                const useOfLeaseNo = this.currentLeaseDetail?.lease?.useOfLeaseNo;
                
                // 遍历所有phases，根据 useOfLeaseNo 重新计算相应的付款金额
                this.currentLeaseDetail.rb.phases.forEach(phase => {
                    // 租金 (useOfLeaseNo 为 '0' 或 '4' 时计算)
                    if ((useOfLeaseNo === '0' || useOfLeaseNo === '4') && phase.rbAccruedAmountRent) {
                        const values = [frequency, phase.rbAccruedAmountRent];
                        phase.rbPaymentAmountRent = calculateFn(values);
                        console.log('Updated rent payment amount:', phase.rbPaymentAmountRent);
                    }
                    
                    // 物管费 (useOfLeaseNo 为 '1' 或 '4' 时计算)
                    if ((useOfLeaseNo === '1' || useOfLeaseNo === '4') && phase.rbAccruedAmountMgmt) {
                        const values = [frequency, phase.rbAccruedAmountMgmt];
                        phase.rbPaymentAmountMgmt = calculateFn(values);
                        console.log('Updated management payment amount:', phase.rbPaymentAmountMgmt);
                    }
                    
                    // 广告费 (useOfLeaseNo 为 '2' 时计算)
                    if (useOfLeaseNo === '2' && phase.rbAccruedAmountAdv) {
                        const values = [frequency, phase.rbAccruedAmountAdv];
                        phase.rbPaymentAmountAdv = calculateFn(values);
                        console.log('Updated advertising payment amount:', phase.rbPaymentAmountAdv);
                    }
                    
                    // 其他费用 (useOfLeaseNo 为 '3' 时计算)
                    if (useOfLeaseNo === '3' && phase.rbAccruedAmountFee) {
                        const values = [frequency, phase.rbAccruedAmountFee];
                        phase.rbPaymentAmountFee = calculateFn(values);
                        console.log('Updated fee payment amount:', phase.rbPaymentAmountFee);
                    }
                });
            },
            /**
             * 处理计提金额变化，重新计算对应的付款金额
             * @param event 事件对象
             * @param phase 当前阶段
             * @param type 费用类型 (rent, mgmt, adv, fee)
             * @param calculateFn 计算函数
             */
            handleAccruedAmountChange(_event: Event, phase: RbPhase, type: 'rent' | 'mgmt' | 'adv' | 'fee', calculateFnName: string = 'calculateTotal') {
                // 获取当前支付频率
                const frequency = this.currentLeaseDetail?.rb?.rbPaymentFrequency;
                if (!frequency) return;
                
                // 获取当前的 useOfLeaseNo 值
                const useOfLeaseNo = this.currentLeaseDetail?.lease?.useOfLeaseNo;
                
                // 检查当前类型是否应该根据 useOfLeaseNo 进行计算
                let shouldCalculate = false;
                switch (type) {
                    case 'rent':
                        shouldCalculate = useOfLeaseNo === '0' || useOfLeaseNo === '4';
                        break;
                    case 'mgmt':
                        shouldCalculate = useOfLeaseNo === '1' || useOfLeaseNo === '4';
                        break;
                    case 'adv':
                        shouldCalculate = useOfLeaseNo === '2';
                        break;
                    case 'fee':
                        shouldCalculate = useOfLeaseNo === '3';
                        break;
                }
                
                if (!shouldCalculate) {
                    console.log(`Skipping calculation for ${type} as useOfLeaseNo is ${useOfLeaseNo}`);
                    return;
                }
                
                // 根据类型获取计提金额
                let accruedAmount = '';
                switch (type) {
                    case 'rent':
                        accruedAmount = phase.rbAccruedAmountRent;
                        break;
                    case 'mgmt':
                        accruedAmount = phase.rbAccruedAmountMgmt;
                        break;
                    case 'adv':
                        accruedAmount = phase.rbAccruedAmountAdv;
                        break;
                    case 'fee':
                        accruedAmount = phase.rbAccruedAmountFee;
                        break;
                }
                
                // 如果计提金额为空，直接返回
                if (!accruedAmount) {
                    return;
                }
                
                // 计算付款金额
                const values = [frequency, accruedAmount];
                // @ts-ignore
                const calculateFn = window[calculateFnName];
                if (typeof calculateFn !== 'function') {
                    console.error(`Function ${calculateFnName} is not defined`);
                    return;
                }
                const paymentAmount = calculateFn(values);
                
                // 根据类型更新付款金额
                switch (type) {
                    case 'rent':
                        phase.rbPaymentAmountRent = paymentAmount;
                        console.log('Updated rent payment amount:', paymentAmount);
                        break;
                    case 'mgmt':
                        phase.rbPaymentAmountMgmt = paymentAmount;
                        console.log('Updated management payment amount:', paymentAmount);
                        break;
                    case 'adv':
                        phase.rbPaymentAmountAdv = paymentAmount;
                        console.log('Updated advertising payment amount:', paymentAmount);
                        break;
                    case 'fee':
                        phase.rbPaymentAmountFee = paymentAmount;
                        console.log('Updated fee payment amount:', paymentAmount);
                        break;
                }
            }
        }))
    }
     /**
     * 验证数据
     * @returns ValidationResult 包含验证结果的对象
     */
     public validateAll(): ValidationResult {
        // 清除之前的错误和警告标记
        this.validator.clearValidationMarks(this.$el);
        
        // 使用类型断言
        const alpineComponent = Alpine.$data(this.$el) as AlpineComponent;
        const context = { $el: this.$el, alpineComponent };
        const data = alpineComponent.data;
        
        // 重置验证结果
        this.validationResult = {
            isValid: true,
            buErrors: [],
            leaseErrors: [],
            buWarnings: [],
            leaseWarnings: []
        };
        
        // 验证 BU 基本信息
        this.validator.validateBuInfoFromDOM(data.finance, this.validationResult);

        
        
        // 验证所有 LeaseDetails
        if (data.finance?.leaseDetails && data.finance.leaseDetails.length > 0) {
            // 清除之前的无效详情
            alpineComponent.invalidDetails = [];
            
            data.finance.leaseDetails.forEach((detail: LeaseDetail, index: number) => {
                const { errors, warnings } = this.validator.validateLeaseDetailFromDOM(detail, index);
                
                // 如果有错误，添加到结果中
                if (errors.length > 0) {
                    this.validationResult.isValid = false;
                    this.validationResult.leaseErrors.push({
                        index,
                        errors
                    });
                    
                    // 更新 Alpine 组件的 invalidDetails 数组
                    if (!alpineComponent.invalidDetails.includes(index)) {
                        alpineComponent.invalidDetails.push(index);
                    }
                }
                
                // 如果有警告，添加到结果中
                if (warnings.length > 0) {
                    this.validationResult.leaseWarnings.push({
                        index,
                        warnings
                    });
                }
            });
        }
        
        // 更新 Alpine 组件的 warningDetails
        alpineComponent.warningDetails = this.validationResult.leaseWarnings.map(item => ({
            index: item.index,
            message: item.warnings.map(w => w.message)
        }));
        
        // 处理当前视图的验证结果
        if (alpineComponent.isLeaseVisible) {
            // 处理 Lease 视图的验证结果
            const currentIndex = alpineComponent.indexLeaseDetail;
            
            // 检查当前 Lease 是否有错误
            const hasCurrentLeaseErrors = this.validationResult.leaseErrors.some(
                item => item.index === currentIndex
            );
            
            // 检查其他 Lease 或 BU 是否有错误
            const hasOtherErrors = this.validationResult.buErrors.length > 0 || 
                this.validationResult.leaseErrors.some(item => item.index !== currentIndex);
            
            if (hasCurrentLeaseErrors) {
                // 如果当前 Lease 有错误，标记当前 Lease 页面
                this.handleLeaseViewValidation(context);
            } else if (hasOtherErrors) {
                // 如果其他 Lease 或 BU 有错误，切换到 BU 视图
                alpineComponent.showBuView();
            }
        } else {
            // 处理 BU 视图的验证结果
            this.handleBuViewValidation(context);
        }
        console.log('validateAll', this.validationResult);
        return this.validationResult;
    }

    /**
     * 处理当前视图的验证结果
     * 显示错误/警告并滚动到第一个错误位置
     */
    private handleValidationForCurrentView(): void {
        const alpineComponent = Alpine.$data(this.$el) as AlpineComponent;
        const context = { $el: this.$el, alpineComponent };
        const _this = this;
        
        // 使用 Alpine.nextTick 而不是 alpineComponent.$nextTick
        Alpine.nextTick(() => {
            if (alpineComponent.isBuVisible) {
                // 处理 BU 视图的验证结果
                _this.handleBuViewValidation(context);
            } else {
                // 处理 Lease 视图的验证结果
                _this.handleLeaseViewValidation(context);
            }
        });
    }

    /**
     * 处理 BU 视图的验证结果
     * @param context 验证上下文
     */
    private handleBuViewValidation(_context: { $el: HTMLElement, alpineComponent: any }): void {
        // 先清除所有验证标记
        this.validator.clearValidationMarks(this.$el);
        
        // 处理警告
        if (this.validationResult.buWarnings.length > 0) {
            const firstWarning = this.validationResult.buWarnings.find(warn => warn.element);
            if (firstWarning && firstWarning.element) {
                // 标记所有警告元素
                this.validationResult.buWarnings.forEach(warning => {
                    if (warning.element) {
                        this.validator.markElementAsWarning(warning.element, warning.message);
                    }
                });
            }
        }
        
        // 处理错误
        if (this.validationResult.buErrors.length > 0) {
            const firstError = this.validationResult.buErrors.find(err => err.element);
            if (firstError && firstError.element) {
                // 滚动到第一个错误元素
                this.scrollToElement(firstError.element as HTMLElement);
                
                // 标记所有错误元素并展开其容器
                this.validationResult.buErrors.forEach(error => {
                    if (error.element) {
                        this.validator.markElementAsError(error.element);
                        this.expandErrorContainer(error.element);
                    }
                });
            }
        } else if (this.validationResult.leaseErrors.length > 0) {
            // 如果 BU 没有错误但 Lease 有错误，展开 Lease 部分
            this.expandAndScrollToLeaseSection();
        }
    }

    /**
     * 处理 Lease 视图的验证结果
     * @param context 验证上下文
     */
    private handleLeaseViewValidation(context: { $el: HTMLElement, alpineComponent: any }): void {
        // 先清除所有验证标记
        this.validator.clearValidationMarks(this.$el);
        
        const currentIndex = context.alpineComponent.indexLeaseDetail;
        
        // 检查当前 Lease 是否有警告
        const currentLeaseWarnings = this.validationResult.leaseWarnings.find(item => item.index === currentIndex);
        if (currentLeaseWarnings && currentLeaseWarnings.warnings.length > 0) {
            // 标记所有警告元素
            currentLeaseWarnings.warnings.forEach(warning => {
                const element = this.validator.findElementByPath(warning.path, context);
                if (element) {
                    this.validator.markElementAsWarning(element, warning.message);
                }
            });
        }
        
        // 检查当前 Lease 是否有错误
        const currentLeaseErrors = this.validationResult.leaseErrors.find(item => item.index === currentIndex);
        if (currentLeaseErrors && currentLeaseErrors.errors.length > 0) {
            const errors = currentLeaseErrors.errors;
            const firstErrorElement = this.validator.findElementByPath(errors[0].path, context);
            
            if (firstErrorElement) {
                // 滚动到第一个错误元素
                this.scrollToElement(firstErrorElement as HTMLElement);
                
                // 标记所有错误元素并展开其容器
                errors.forEach(error => {
                    const element = this.validator.findElementByPath(error.path, context);
                    if (element) {
                        this.validator.markElementAsError(element);
                        this.expandErrorContainer(element);
                    }
                });
            }
        }
    }

    

    /**
     * 获取数据
     * @returns 
     */
    public outputData(): boolean {
        const alpineComponent =Alpine.$data(this.$el)
        //@ts-ignore
        return  alpineComponent.data
    }

    /**
     * 更新所有租赁详情中的 RB 阶段日期
     * @param beginDate 开始日期 (YYYY-MM-DD 格式)
     * @param endDate 结束日期 (YYYY-MM-DD 格式)
     */
    public updateRbPhaseDates(beginDate: string, endDate: string): void {
        const alpineComponent = Alpine.$data(this.$el) as AlpineComponent;
        const data = alpineComponent.data;

        // 验证日期格式
        if (!/^\d{4}-\d{2}-\d{2}$/.test(beginDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
            console.error('Invalid date format. Please use YYYY-MM-DD format.');
            return;
        }

        // 验证日期逻辑
        const beginDateObj = new Date(beginDate);
        const endDateObj = new Date(endDate);
        if (beginDateObj > endDateObj) {
            console.error('Begin date cannot be later than end date.');
            return;
        }

        // 更新所有租赁详情中的阶段日期
        data.finance?.leaseDetails?.forEach(detail => {
            // 只有当用途不是广告费('2')且不是其他费用('3')时才更新
            if (detail.lease?.useOfLeaseNo !== '2' && detail.lease?.useOfLeaseNo !== '3') {
                if (detail.rb?.phases && detail.rb.phases.length > 0) {
                    detail.rb.phases.forEach(phase => {
                        phase.rbBeginDate = beginDate;
                        phase.rbEndDate = endDate;
                    });
                }
            }
        });

        // 触发 Alpine 更新
        if (typeof this.$el.dispatchEvent === 'function') {
            this.$el.dispatchEvent(new Event('input'));
        }
    }

    /**
     * 更新所有租赁详情中的 SOV 规则日期
     * @param beginDate 开始日期 (YYYY-MM-DD 格式)
     * @param endDate 结束日期 (YYYY-MM-DD 格式)
     */
    public updateSovRuleDates(beginDate: string, endDate: string): void {
        const alpineComponent = Alpine.$data(this.$el) as AlpineComponent;
        const data = alpineComponent.data;

        // 验证日期格式
        if (!/^\d{4}-\d{2}-\d{2}$/.test(beginDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
            console.error('Invalid date format. Please use YYYY-MM-DD format.');
            return;
        }

        // 验证日期逻辑
        const beginDateObj = new Date(beginDate);
        const endDateObj = new Date(endDate);
        if (beginDateObj > endDateObj) {
            console.error('Begin date cannot be later than end date.');
            return;
        }

        // 更新所有租赁详情中的 SOV 规则日期
        data.finance?.leaseDetails?.forEach(detail => {
            // 只有当用途不是广告费('2')且不是其他费用('3')时才更新
            if (detail.lease?.useOfLeaseNo !== '2' && detail.lease?.useOfLeaseNo !== '3') {
                if (detail.sov?.sovRules && detail.sov.sovRules.length > 0) {
                    detail.sov.sovRules.forEach(rule => {
                        rule.sovBeginDate = beginDate;
                        rule.sovEndDate = endDate;
                    });
                }
            }
        });

        // 触发 Alpine 更新
        if (typeof this.$el.dispatchEvent === 'function') {
            this.$el.dispatchEvent(new Event('input'));
        }
    }

    /**
     * 同时更新所有租赁详情中的 RB 阶段日期和 SOV 规则日期
     * @param beginDate 开始日期 (YYYY-MM-DD 格式)
     * @param endDate 结束日期 (YYYY-MM-DD 格式)
     * @returns 包含操作结果的对象，success 表示是否成功，errorMessage 包含错误信息（如果有）
     */
    public updateAllDates(beginDate: string, endDate: string): { success: boolean, errorMessage?: string } {
        try {
            const alpineComponent = Alpine.$data(this.$el) as AlpineComponent;
            const data = alpineComponent.data;

            // 验证数据对象
            if (!data || !data.finance) {
                return { success: false, errorMessage: '无法获取财务数据' };
            }

            // 验证日期格式
            if (!/^\d{4}-\d{2}-\d{2}$/.test(beginDate) || !/^\d{4}-\d{2}-\d{2}$/.test(endDate)) {
                console.error('Invalid date format. Please use YYYY-MM-DD format.');
                return { success: false, errorMessage: '日期格式无效，请使用 YYYY-MM-DD 格式' };
            }

            // 验证日期逻辑
            const beginDateObj = new Date(beginDate);
            const endDateObj = new Date(endDate);
            if (beginDateObj > endDateObj) {
                console.error('Begin date cannot be later than end date.');
                return { success: false, errorMessage: '开始日期不能晚于结束日期' };
            }

            // 检查是否有可更新的租赁详情
            if (!data.finance.leaseDetails || data.finance.leaseDetails.length === 0) {
                return { success: false, errorMessage: '没有可更新的租赁详情' };
            }

            let updatedCount = 0;

            // 同时更新所有租赁详情中的 RB 阶段日期和 SOV 规则日期
            data.finance.leaseDetails.forEach(detail => {
                // 只有当用途不是广告费('2')且不是其他费用('3')时才更新
                if (detail.lease?.useOfLeaseNo !== '2' && detail.lease?.useOfLeaseNo !== '3') {
                    let detailUpdated = false;
                    
                    // 更新 RB 阶段日期
                    if (detail.rb?.phases && detail.rb.phases.length > 0) {
                        detail.rb.phases.forEach(phase => {
                            phase.rbBeginDate = beginDate;
                            phase.rbEndDate = endDate;
                        });
                        detailUpdated = true;
                    }
                    
                    // 更新 SOV 规则日期
                    if (detail.sov?.sovRules && detail.sov.sovRules.length > 0) {
                        detail.sov.sovRules.forEach(rule => {
                            rule.sovBeginDate = beginDate;
                            rule.sovEndDate = endDate;
                        });
                        detailUpdated = true;
                    }

                    if (detailUpdated) {
                        updatedCount++;
                    }
                }
            });

            // 检查是否有任何更新
            if (updatedCount === 0) {
                return { success: false, errorMessage: '没有符合条件的租赁详情需要更新' };
            }

            // 触发 Alpine 更新
            if (typeof this.$el.dispatchEvent === 'function') {
                this.$el.dispatchEvent(new Event('input'));
            }

            return { success: true };
        } catch (error) {
            console.error('Error updating dates:', error);
            return { 
                success: false, 
                errorMessage: `更新日期时发生错误: ${error instanceof Error ? error.message : String(error)}` 
            };
        }
    }

}


