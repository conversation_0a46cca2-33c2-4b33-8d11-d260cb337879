<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>Financial System</title>
</head>
<body>
<div id="app">
    <div id="financialSystemComponent" x-ref="financialSystem" x-data="financialSystemComponent" x-init="init()" class="tab-result tab-result2">
        <!-- 测试 select -->
        <select id="useOfLeaseNoMapping" style="display: none;">
            <option value="0">0:租金</option>
            <option value="1">1:物管费</option>
            <option value="2">2:广告</option>
            <option value="3">3:其他费用</option>
            <option value="4">4:租金+物管费</option>
        </select>
        <!-- bu 基本信息 -->
        <div id="buContainer" x-show="isBuVisible" class="baseInfo display-flex-column">
            <div class="aimingPoint">
                <ul class="display-flex-row align-items-center">
                    <li class="active" data-target="basic-info">基础信息</li>
                    <li data-target="bu-level">BU Level</li>
                    <li data-target="additional-info">附加信息</li>
                    <li data-target="lease" :class="invalidDetails.length > 0?'error':''" class="">Lease</li>
                    <li data-target="remarks">备注</li>
                </ul>
            </div>
            <div class="pointResult hasblack-tip">
                <!--       basic-info        -->
                <div class="po-child open" id="basic-info">
                    <div x-on:click="toggleContent($event)" class="po-child-header display-flex-row align-items-center">
                        <label>基础信息</label>
                        <a class="put-right">收起</a>
                    </div>
                    <div class="po-child-content">
                        <div class="w-public-input display-flex-column ">
                            <label class="form-label w-form-label">
                                <b class="not_null">*</b>
                                餐厅编号
                                <i class="error-icon"></i>
                            </label>
                            <input type="text" class="sf-form-control w-form-control" placeholder="请输入餐厅编号" x-model="data.finance.buCode" required="">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null">*</b>
                                餐厅名称
                                <i class="error-icon"></i>
                            </label>
                            <input type="text" class="sf-form-control w-form-control" placeholder="请输入餐厅名称" x-model="data.finance.buName" required="">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null">*</b>
                                财务市场
                                <i class="error-icon"></i>
                            </label>

                                <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.finMarket" placeholder="请选择财务市场" required="">
                                <option value="青岛财务部">青岛财务部</option>
                                <option value="长沙财务部">长沙财务部</option>
                                <option value="西安财务部">西安财务部</option>
                                <option value="武汉财务部">武汉财务部</option>
                                <option value="南京财务部">南京财务部</option>
                                <option value="福州财务部">福州财务部</option>
                                <option value="安徽财务部">安徽财务部</option>
                                <option value="上海必胜客财务部">上海必胜客财务部</option>
                                <option value="云贵财务部">云贵财务部</option>
                                <option value="哈尔滨财务部">哈尔滨财务部</option>
                                <option value="上海肯德基财务部">上海肯德基财务部</option>
                                <option value="天津财务部">天津财务部</option>
                                <option value="小肥羊财务部">小肥羊财务部</option>
                                <option value="广西财务部">广西财务部</option>
                                <option value="成都财务部">成都财务部</option>
                                <option value="无锡财务部">无锡财务部</option>
                                <option value="晋蒙财务部">晋蒙财务部</option>
                                <option value="江西财务部">江西财务部</option>
                                <option value="浙江必胜客市场财务部">浙江必胜客市场财务部</option>
                                <option value="深圳财务部">深圳财务部</option>
                                <option value="郑州财务部">郑州财务部</option>
                                <option value="北京财务部">北京财务部</option>
                                <option value="黄记煌财务部">黄记煌财务部</option>
                                <option value="RSC财务部">RSC财务部</option>
                                <option value="沈阳财务部">沈阳财务部</option>
                                <option value="Lavazza华东市场财务部">Lavazza华东市场财务部</option>
                                <option value="杭州财务部">杭州财务部</option>
                                <option value="广州财务部">广州财务部</option>
                                <option value="苏州财务部">苏州财务部</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-1" aria-haspopup="listbox" aria-expanded="false" title="广州财务部"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">广州财务部</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 414.148px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-1" tabindex="-1" style="max-height: 406.148px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-1-27"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-1-0" tabindex="0"><span class="text">青岛财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-1" tabindex="0"><span class="text">长沙财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-2" tabindex="0"><span class="text">西安财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-3" tabindex="0"><span class="text">武汉财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-4" tabindex="0"><span class="text">南京财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-5" tabindex="0"><span class="text">福州财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-6" tabindex="0"><span class="text">安徽财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-7" tabindex="0"><span class="text">上海必胜客财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-8" tabindex="0"><span class="text">云贵财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-9" tabindex="0"><span class="text">哈尔滨财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-10" tabindex="0"><span class="text">上海肯德基财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-11" tabindex="0"><span class="text">天津财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-12" tabindex="0"><span class="text">小肥羊财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-13" tabindex="0"><span class="text">广西财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-14" tabindex="0"><span class="text">成都财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-15" tabindex="0"><span class="text">无锡财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-16" tabindex="0"><span class="text">晋蒙财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-17" tabindex="0"><span class="text">江西财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-18" tabindex="0"><span class="text">浙江必胜客市场财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-19" tabindex="0"><span class="text">深圳财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-20" tabindex="0"><span class="text">郑州财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-21" tabindex="0"><span class="text">北京财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-22" tabindex="0"><span class="text">黄记煌财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-23" tabindex="0"><span class="text">RSC财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-24" tabindex="0"><span class="text">沈阳财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-25" tabindex="0"><span class="text">Lavazza华东市场财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-26" tabindex="0"><span class="text">杭州财务部</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-1-27" tabindex="0" aria-setsize="29" aria-posinset="28" aria-selected="true"><span class="text">广州财务部</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-1-28" tabindex="0"><span class="text">苏州财务部</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                业务类别
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.bizType" placeholder="请选择业务类别" @change="if(data.finance.bizType>1)data.finance.openDate = ''">
                                <option value="0">新店交付</option>
                                <option value="1">新店起租</option>
                                <option value="2">续约</option>
                                <option value="3">变更</option>
                                <option value="4">关店</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-2" aria-haspopup="listbox" aria-expanded="false" title="新店交付"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">新店交付</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 500.148px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-2" tabindex="-1" style="max-height: 492.148px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-2-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-2-0" tabindex="0" aria-setsize="5" aria-posinset="1" aria-selected="true"><span class="text">新店交付</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-2-1" tabindex="0"><span class="text">新店起租</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-2-2" tabindex="0"><span class="text">续约</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-2-3" tabindex="0"><span class="text">变更</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-2-4" tabindex="0"><span class="text">关店</span></a></li></ul></div></div></div>
                        </div>
                        <template x-if="data.finance.bizType=='3'">
                            <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <label class="form-label w-form-label">
                                    变更合同类型
                                    <i class="error-icon"></i>
                                </label>
                                <select class="w-form-select" x-model="data.finance.changeContType" placeholder="请选择业务类别">
                                    <option value="税率变更">税率变更</option>
                                    <option value="主体变更">主体变更</option>
                                    <option value="租金变更">租金变更</option>
                                    <option value="IE">IE</option>
                                    <option value="面积变更">面积变更</option>
                                    <option value="其他">其他</option>
                                </select>
                            </div>
                        </template>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                合同类型
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.contractType" placeholder="请选择合同类型">
                                <option value="0">标准合同</option>
                                <option value="1">非标合同</option>
                                <option value="2">SP合同</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-3" aria-haspopup="listbox" aria-expanded="false" title="标准合同"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">标准合同</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 586.148px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-3" tabindex="-1" style="max-height: 578.148px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-3-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-3-0" tabindex="0" aria-setsize="3" aria-posinset="1" aria-selected="true"><span class="text">标准合同</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-3-1" tabindex="0"><span class="text">非标合同</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-3-2" tabindex="0"><span class="text">SP合同</span></a></li></ul></div></div></div>
                        </div>
                    </div>
                </div>
                <!--      bu-level          -->
                <div class="po-child open" id="bu-level">
                    <div x-on:click="toggleContent($event)" class="po-child-header display-flex-row align-items-center">
                        <label>BU Level</label>
                        <a class="put-right">收起</a>
                    </div>
                    <div class="po-child-content">
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                合同签署日期
                                <i class="error-icon"></i>
                            </label>
                            <input id="signDate" type="text" class=" sf-form-control w-form-control w-form-control-date" placeholder="请选择合同签署日期" x-model="data.finance.signDate" autocomplete="off" data-init="{'dateTime':true,'endDateEl':'openDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">开业日期
                                <i class="error-icon"></i>
                            </label>
                            <input id="openDate" x-bind:disabled="data.finance.bizType > 1" type="text" autocomplete="off" class=" sf-form-control w-form-control w-form-control-date" placeholder="请选择开业日期" x-model="data.finance.openDate" data-init="{'dateTime':true,'startDateEl':'signDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                租赁期满后是否恢复原状
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.returnAreaYes" placeholder="请选择租赁期满后是否恢复原状">
                                <option value="N">实际状态</option>
                                <option value="Y">毛坯状态</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-4" aria-haspopup="listbox" aria-expanded="false" title="实际状态"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">实际状态</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 894.148px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-4" tabindex="-1" style="max-height: 886.148px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-4-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-4-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">实际状态</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-4-1" tabindex="0"><span class="text">毛坯状态</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                是否禁止转租
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.noSublet" placeholder="请选择是否禁止转租">
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-5" aria-haspopup="listbox" aria-expanded="false" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 980.148px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-5" tabindex="-1" style="max-height: 972.148px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-5-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-5-0" tabindex="0"><span class="text">是</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-5-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">否</span></a></li></ul></div></div></div>
                        </div>
                        <!-- 修改验证-->
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label" title="计租面积>建筑面积>使用面积">
                                <b class="not_null" x-show="data.finance.bizType == '0' || data.finance.bizType == '1'">*</b>
                                计租面积(SM)
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" placeholder="请输入计租面积" x-model="data.finance.areaSM" class="sf-form-control w-form-control" x-bind:required="data.finance.bizType === '0' || data.finance.bizType === '1'" data-init="{ alias: 'numeric', suffix: ' ', rightAlign: false, digits: 2, digitsOptional: true, placeholder: '', autoUnmask: true }" required="required" inputmode="decimal">
                        </div>
                        <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null" x-show="data.finance.bizType == '0' || data.finance.bizType == '1'">*</b>
                                分楼层面积
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" x-model="data.finance.areaRemark" class="sf-form-control w-form-control " placeholder="请输入分楼层面积" x-bind:required="data.finance.bizType === '0' || data.finance.bizType === '1'" required="required">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                房产证生效日
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.signDate" id="certEffectiveDate" type="text" class=" sf-form-control w-form-control w-form-control-date" placeholder="请选择房产证生效日" data-init="{'dateTime':true,'endDateEl':'certExpiryDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                房产证到期日
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.certExpiryDate" id="certExpiryDate" type="text" class=" sf-form-control w-form-control w-form-control-date" placeholder="请选择房产证到期日" data-init="{'dateTime':true,'startDateEl':'certEffectiveDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                抽成租金为单一比例or多比例?
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.leaseTypePlanning" placeholder="请选择抽成租金为单一比例or多比例?">
                                <option value="P1">P1:单一比例</option>
                                <option value="P2">P2:多比例</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-6" aria-haspopup="listbox" aria-expanded="false" title="P1:单一比例"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">P1:单一比例</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1414.91px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-6" tabindex="-1" style="max-height: 1406.91px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-6-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-6-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">P1:单一比例</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-6-1" tabindex="0"><span class="text">P2:多比例</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                租金类型
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.leaseTypeDE" placeholder="请选择租金类型">
                                <option value="D1">D1:纯抽成</option>
                                <option value="D2">D2:纯固定</option>
                                <option value="D3">D3:保底+抽成</option>
                                <option value="D4">D4:保底与抽成取高</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-7" aria-haspopup="listbox" aria-expanded="false" title="D2:纯固定"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">D2:纯固定</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1500.91px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-7" tabindex="-1" style="max-height: 1492.91px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-7-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-7-0" tabindex="0"><span class="text">D1:纯抽成</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-7-1" tabindex="0" aria-setsize="4" aria-posinset="2" aria-selected="true"><span class="text">D2:纯固定</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-7-2" tabindex="0"><span class="text">D3:保底+抽成</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-7-3" tabindex="0"><span class="text">D4:保底与抽成取高</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                RE/复核用
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select disabled w-form-select bs3 dropup"><select class="w-form-select" disabled="disabled" x-model="data.finance.leaseTypeRE" placeholder="请选择RE/复核用">
                                <option value="R1">一级复核</option>
                                <option value="R2">二级复核</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle disabled btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-8" aria-haspopup="listbox" aria-expanded="false" aria-disabled="true" title="二级复核"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">二级复核</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1586.91px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-8" tabindex="-1" style="max-height: 1578.91px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-8-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-8-0" tabindex="0"><span class="text">一级复核</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-8-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">二级复核</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                定金金额（元）
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input maxlength="20" x-model="data.finance.depositAmount" type="text" class=" sf-form-control w-form-control" placeholder="请输入定金金额" data-init="{ 'alias': 'currency', 'prefix': '', 'groupSeparator': ',', 'radixPoint': '.', 'digits': 2, 'digitsOptional': false, 'autoUnmask': true, 'allowMinus': false, 'suffix': '元', 'rightAlign': false }" inputmode="decimal">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                初始直接费用金额(元)
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input maxlength="20" x-model="data.finance.idcAmount" type="text" class=" sf-form-control w-form-control " placeholder="请输入IDC金额" data-init="{ alias: 'currency', prefix: '', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: false, suffix: '元', rightAlign: false }" inputmode="decimal">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                补偿金金额（元）
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input maxlength="20" x-model="data.finance.leaseIncentives" type="text" class=" sf-form-control w-form-control " placeholder="请输入补偿金金额" data-init="{ alias: 'currency', prefix: '', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: false, suffix: '元', rightAlign: false }" inputmode="decimal">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                是否转入中转科目
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.transferSubject" placeholder="请选择是否转入中转科目">
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-9" aria-haspopup="listbox" aria-expanded="false" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1930.91px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-9" tabindex="-1" style="max-height: 1922.91px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-9-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-9-0" tabindex="0"><span class="text">是</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-9-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">否</span></a></li></ul></div></div></div>
                        </div>
                    </div>
                </div>
                <!--       附加信息         -->
                <div class="po-child open" id="additional-info">
                    <div x-on:click="toggleContent($event)" class="po-child-header display-flex-row align-items-center">
                        <label>附加信息</label>
                        <a class="put-right">收起</a>
                    </div>
                    <div class="po-child-content">
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                RE Notes
                                <i class="error-icon"></i>
                            </label>
                            <input x-model="data.finance.reNotes" type="text" class="sf-form-control w-form-control" placeholder="请输入RE Notes">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                保证金金额（元）
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input maxlength="20" x-model="data.finance.bondAmount" type="text" class="sf-form-control w-form-control" placeholder="请输入保证金金额" data-init="{ alias: 'currency', prefix: '', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: false, suffix: '元', rightAlign: false }" inputmode="decimal">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                返还时间
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.bondReturnDate" id="bondReturnDate" type="text" class=" sf-form-control w-form-control w-form-control-date" placeholder="请选择返还时间" data-init="{'dateTime':true,regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                保函金额（元）
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.guaranteeAmount" type="text" class="sf-form-control w-form-control" placeholder="请输入保函金额" data-init="{ alias: 'currency', prefix: '', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: false, suffix: '元', rightAlign: false }" inputmode="decimal">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                策略联盟业主
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="data.finance.strategicAlliance" placeholder="请选择策略联盟业主">
                                <option value="0">策略联盟业主</option>
                                <option value="1">非策略联盟业主</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-10" aria-haspopup="listbox" aria-expanded="false" title="非策略联盟业主"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">非策略联盟业主</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2410.91px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-10" tabindex="-1" style="max-height: 2402.91px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-10-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-10-0" tabindex="0"><span class="text">策略联盟业主</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-10-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">非策略联盟业主</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                合同甲方
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.partyA" type="text" class="sf-form-control w-form-control" placeholder="请输入">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                甲方地址
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.addOfPartyA" type="text" class="sf-form-control w-form-control" placeholder="请输入甲方地址">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                合同乙方（我司市场法人）
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.partyB" type="text" class="sf-form-control w-form-control" placeholder="请输入合同乙方">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                乙方地址
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.addOfPartyB" type="text" class="sf-form-control w-form-control" placeholder="请输入乙方地址">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                租赁店铺地址
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input x-model="data.finance.addOfStore" type="text" class="sf-form-control w-form-control" placeholder="请输入租赁店铺地址">
                        </div>
                    </div>
                </div>
                <!--       Lease         -->
                <div class="po-child open" id="lease">
                    <div x-on:click="toggleContent($event)" class="po-child-header display-flex-row align-items-center">
                        <label>Lease</label>
                        <a class="put-right">收起</a>
                    </div>
                    <div class="po-child-content">
                        <table class="table offcanvas-body-table offcanvas-body-table-pub">
                            <thead>
                            <tr>
                                <th style="width:15%;" class="text-center">编号</th>
                                <th style="width:35%;">LeaseNo.用途</th>
                                <th style="width:24%;">LeaseNo.</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                            <template x-for="(detail, index) in data.finance.leaseDetails">
                                <tr x-init="initInputDateMask($el)" x-id="['lease-row-' + index]">
                                    <!--修改过-->
                                    <td class="text-center" :class="checkValid(index) ? 'error' : ''">
                                        <i class="error-icon" x-show="checkValid(index)"></i>
                                        <span x-text="index + 1"></span>
                                    </td>
                                    <td :id="'lease-useOfLeaseNo-' + index">
                                        <select x-bind:disabled="detail.objectId!=''" x-model="detail.lease.useOfLeaseNo" class="w-form-select" x-on:change="handleUseOfLeaseNoChange(detail)">
                                            <option value="0">0:租金</option>
                                            <option value="1">1:物管费</option>
                                            <option value="2">2:广告</option>
                                            <option value="3">3:其他费用</option>
                                            <option value="4">4:租金+物管费</option>
                                        </select>
                                    </td>
                                    <td :id="'lease-leaseCode-' + index">
                                        <!--修改验证-->
                                        <input type="text" x-model="detail.lease.leaseCode" placeholder="请输入LeaseNo." class="sf-form-control w-form-control" data-init="{
                                                    regex:'^\d+$',
                                                    warning: '^\d{7,8}$',
                                                    placeholder:''
                                                }">
                                    </td>
                                    <td>
                                        <template x-if="detail.objectId!=''">
                                            <a class="qc" x-on:click="showDetails(index)">详情</a>
                                        </template>
                                        <a class="qc" x-on:click="showDetails(index)">详情</a>
                                        <template x-if="detail.objectId==''">
                                            <a x-show="detail.objectId==''" class="qc" x-on:click="saveDetail(index, saveLeaseDetail)">保存</a>
                                        </template>
                                        <a class="qc" x-on:click="deleteLease(index, validateLeaseDelete)">删除</a>
                                    </td>
                                </tr>
                            </template>
                            </tbody>
                        </table>
                        <div class="add-table-child">
                            <a x-on:click="addDetail()" class="display-flex-row align-items-center">添加</a>
                        </div>
                    </div>
                </div>
                <!--       备注         -->
                <div class="po-child open" id="remarks">
                    <div x-on:click="toggleContent($event)" class="po-child-header display-flex-row align-items-center">
                        <label>备注</label>
                        <a class="put-right">收起</a>
                    </div>
                    <div class="po-child-content">
                        <div class="w-public-input display-flex-column">
                            <textarea x-model="data.finance.desc" class="sf-form-control resizable-textarea w-form-control w-form-textarea" placeholder="请输入"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--leases 单独弹出页  -->
        <div id="leaseContainer" x-show="isLeaseVisible" class="baseInfo baseInfo-lease display-flex-column" style="">
            <input type="hidden" x-model="currentLeaseDetail.lease.leaseCode" required="">
            <div class="aimingPoint">
                <button class="popup-btn-grey" @click="goBack()">返回</button>
                <div class="leaseInfo display-flex-row align-items-center">
                    <label x-text="'LeaseNo.' + (currentLeaseDetail ? currentLeaseDetail.lease.leaseCode : '')">LeaseNo.</label>
                    <span>
                            <i x-text="'餐厅：'+data.finance.buCode">餐厅：80054169</i>
                            <i x-text="'用途：' + (currentLeaseDetail ? getUseOfLeaseNoText(currentLeaseDetail.lease.useOfLeaseNo) : '')">用途：0:租金</i>
                        </span>
                </div>
                <ul class="display-flex-row align-items-center">
                    <li class="active" data-target="LeaseDetail_lease">Lease</li>
                    <li data-target="LeaseDetail_br" class="">RB</li>
                    <li data-target="LeaseDetail_sov">SOV</li>
                </ul>
            </div>
            <div class="pointResult hasblack-tip">
                <!-- lease -->
                <div class="po-child open" id="LeaseDetail_lease">
                    <div class="po-child-header display-flex-row align-items-center">
                        <label>Landlord</label>
                    </div>
                    <div class="po-child-content">
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null">*</b>
                                LeaseNo.
                                <i class="error-icon"></i>
                                <i class="warning-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <!--修改验证-->
                            <div class="form-label w-form-warning"></div>
                            <input x-model="currentLeaseDetail.lease.leaseCode" type="text" class="sf-form-control w-form-control" data-init="{
                                        regex:'^\d+$',
                                        warning: '^\d{7,8}$',
                                        warningMessage: 'LeaseNo.长度必须是 7 到 8 位数字！',
                                        placeholder:''
                                    }" required="" inputmode="text" placeholder="请输入LeaseNo.">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null" x-show="data.finance.bizType == '0' || data.finance.bizType == '1'">*</b>
                                租赁名称
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" x-model="currentLeaseDetail.lease.rentName" class="sf-form-control w-form-control" placeholder="请输入" x-bind:required="data.finance.bizType === '0' || data.finance.bizType === '1'" required="required">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                租赁类型
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.rentType" placeholder="请选择租赁类型">
                                <option value="AD">AD:RSC标识符</option>
                                <option value="AP">AP:公寓</option>
                                <option value="BN">BN:Business Rental - 新合同</option>
                                <option value="BO">BO:Business Rental - 老合同</option>
                                <option value="CC">CC:电话呼叫中心</option>
                                <option value="CK">CK:中央厨房</option>
                                <option value="CO">CO:商务办公室</option>
                                <option value="ID">ID:工业</option>
                                <option value="MP">MP:借调经理租房</option>
                                <option value="RR">RR:加盟店上手租约</option>
                                <option value="RS">RS:餐馆</option>
                                <option value="SG">SG:广告位租赁</option>
                                <option value="SI">SI:内部转租</option>
                                <option value="ST">ST:Short Lease</option>
                                <option value="SU">SU:转租第三方</option>
                                <option value="WH">WH:仓库/储存</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-14" aria-haspopup="listbox" aria-expanded="false" title="RS:餐馆"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">RS:餐馆</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 511.766px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-14" tabindex="-1" style="max-height: 503.766px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-14-10"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-14-0" tabindex="0"><span class="text">AD:RSC标识符</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-1" tabindex="0"><span class="text">AP:公寓</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-2" tabindex="0"><span class="text">BN:Business Rental - 新合同</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-3" tabindex="0"><span class="text">BO:Business Rental - 老合同</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-4" tabindex="0"><span class="text">CC:电话呼叫中心</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-5" tabindex="0"><span class="text">CK:中央厨房</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-6" tabindex="0"><span class="text">CO:商务办公室</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-7" tabindex="0"><span class="text">ID:工业</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-8" tabindex="0"><span class="text">MP:借调经理租房</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-9" tabindex="0"><span class="text">RR:加盟店上手租约</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-14-10" tabindex="0" aria-setsize="16" aria-posinset="11" aria-selected="true"><span class="text">RS:餐馆</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-11" tabindex="0"><span class="text">SG:广告位租赁</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-12" tabindex="0"><span class="text">SI:内部转租</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-13" tabindex="0"><span class="text">ST:Short Lease</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-14" tabindex="0"><span class="text">SU:转租第三方</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-14-15" tabindex="0"><span class="text">WH:仓库/储存</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null" x-show="data.finance.bizType == '0' || data.finance.bizType == '1'">*</b>
                                业主JDE号
                                <i class="error-icon"></i>
                            </label>
                            <input type="text" maxlength="8" x-model="currentLeaseDetail.lease.landlordCode" class="sf-form-control w-form-control" placeholder="请输入业主JDE号(必须5,7或者8位数字)" data-init="{regex:'^\d{5,8}$',placeholder:''}" x-bind:required="data.finance.bizType === '0' || data.finance.bizType === '1'" required="required" inputmode="text">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null" x-show="data.finance.bizType == '0' || data.finance.bizType == '1'">*</b>
                                业主名称
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" x-model="currentLeaseDetail.lease.landlordName" class="sf-form-control w-form-control" placeholder="请输入业主JDE号(必须5,7或者8位数字)" x-bind:required="data.finance.bizType === '0' || data.finance.bizType === '1'" required="required">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null" x-show="data.finance.bizType == '0' || data.finance.bizType == '1'">*</b>
                                实际收款人JDE号
                                <i class="error-icon"></i>
                            </label>
                            <input type="text" x-model="currentLeaseDetail.lease.receivedCode" class="sf-form-control w-form-control" placeholder="实际收款人JDE号(必须5,7或者8位数字)" data-init="{ regex: '^\d{5,8}$',placeholder:'' }" x-bind:required="data.finance.bizType === '0' || data.finance.bizType === '1'" required="required" inputmode="text">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                <b class="not_null" x-show="data.finance.bizType == '0' || data.finance.bizType == '1'">*</b>
                                实际收款人名称
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" x-model="currentLeaseDetail.lease.receivedName" class="sf-form-control w-form-control" placeholder="请输入实际收款人名称" x-bind:required="data.finance.bizType === '0' || data.finance.bizType === '1'" required="required">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                开票人JDE号(三流不合一时需要填写)
                                <i class="error-icon"></i>
                            </label>
                            <input type="text" x-model="currentLeaseDetail.lease.taxplayerJde" class="sf-form-control w-form-control" placeholder="请输入开票人JDE号(必须5,7或者8位数字)" data-init="{ regex: '^\d{5}$|^\d{7}$|^\d{8}$',placeholder:'' }" inputmode="text">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                开票人名称(三流不合一时需要填写)
                                <i class="error-icon"></i>
                            </label>
                            <input type="text" x-model="currentLeaseDetail.lease.taxplayerName" class="sf-form-control w-form-control" placeholder="请输入开票人名称">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                房产交付日期
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" autocomplete="off" id="leaseBeginDate" x-model="currentLeaseDetail.lease.beginDate" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择房产交付日期" data-init="{'dateTime':true,regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                免租开始日期
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" autocomplete="off" x-model="currentLeaseDetail.lease.freeBeginDate" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择免租开始日期" id="leaseFreeBeginDate" data-init="{'dateTime':true,'endDateEl':'leaseFreeEndDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                免租结束日期
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" autocomplete="off" x-model="currentLeaseDetail.lease.freeEndDate" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择免租结束日期" id="leaseFreeEndDate" data-init="{'dateTime':true,'startDateEl':'leaseFreeBeginDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                起算日期
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" autocomplete="off" x-model="currentLeaseDetail.lease.startRentDate" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择起算日期" id="leaseStartRentDate" data-init="{'dateTime':true,'endDateEl':'leaseOrgEndDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                租赁期限结束日期
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" autocomplete="off" x-model="currentLeaseDetail.lease.orgEndDate" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择租赁期限结束日期" id="leaseOrgEndDate" data-init="{'dateTime':true,'startDateEl':'leaseStartRentDate',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                是否与同组内其他lease合并计算
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.mergeOthersYes" placeholder="请选择是否与同组内其他lease合并计算" @change="if(currentLeaseDetail.lease.mergeOthersYes == 'Y')currentLeaseDetail.lease.strategyAlianceYes = 'N',currentLeaseDetail.lease.leaseGroup = ''">
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-15" aria-haspopup="listbox" aria-expanded="false" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1553.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-15" tabindex="-1" style="max-height: 1545.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-15-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-15-0" tabindex="0"><span class="text">是</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-15-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">否</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <template x-if="currentLeaseDetail.lease.mergeOthersYes == 'Y'">
                                <div data-init="initInputDateMask($el)">
                                    <label class="form-label w-form-label">
                                        <b class="not_null">*</b>
                                        租赁组别
                                        <i class="error-icon"></i>
                                    </label>
                                    <input type="text" x-model="currentLeaseDetail.lease.leaseGroup" data-init="{ regex: '\\d{1,2}',placeholder:''}" class="sf-form-control w-form-control " placeholder="请输入租赁组别" required="">
                                </div>
                            </template>
                            <template x-if="currentLeaseDetail.lease.mergeOthersYes !== 'Y'">
                                <div x-init="initInputDateMask($el)">
                                    <label class="form-label w-form-label">
                                        租赁组别
                                        <i class="error-icon"></i>
                                    </label>
                                    <input type="text" x-model="currentLeaseDetail.lease.leaseGroup" disabled="" class="sf-form-control w-form-control " placeholder="请输入租赁组别">
                                </div>
                            </template><div x-init="initInputDateMask($el)">
                                    <label class="form-label w-form-label">
                                        租赁组别
                                        <i class="error-icon"></i>
                                    </label>
                                    <input type="text" x-model="currentLeaseDetail.lease.leaseGroup" disabled="" class="sf-form-control w-form-control " placeholder="请输入租赁组别">
                                </div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                是否独立第三方物管费
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select disabled w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.strategyAlianceYes" placeholder="请选择是否独立第三方物管费" x-bind:disabled="currentLeaseDetail.lease.mergeOthersYes == 'Y' || currentLeaseDetail.lease.useOfLeaseNo == '0'" disabled="disabled">
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle disabled btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-16" aria-haspopup="listbox" aria-expanded="false" aria-disabled="true" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1725.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-16" tabindex="-1" style="max-height: 1717.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-16-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-16-0" tabindex="0"><span class="text">是</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-16-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">否</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                年度通知单
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.yearlyNote" placeholder="请选择年度通知单">
                                <option value="Y">Y:提供年度结算通知单</option>
                                <option value="N">N:无需提供年度结算通知单</option>
                                <option value="YO">YO:提供年度结算通知单－允许手工修改</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-17" aria-haspopup="listbox" aria-expanded="false" title="N:无需提供年度结算通知单"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">N:无需提供年度结算通知单</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1811.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-17" tabindex="-1" style="max-height: 1803.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-17-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-17-0" tabindex="0"><span class="text">Y:提供年度结算通知单</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-17-1" tabindex="0" aria-setsize="3" aria-posinset="2" aria-selected="true"><span class="text">N:无需提供年度结算通知单</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-17-2" tabindex="0"><span class="text">YO:提供年度结算通知单－允许手工修改</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                通知单是否发给收款人
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.receiverNote" placeholder="请选择通知单是否发给收款人">
                                <option value="">否</option>
                                <option value="R">是</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle bs-placeholder btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-18" aria-haspopup="listbox" aria-expanded="false" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1897.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-18" tabindex="-1" style="max-height: 1889.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-18-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-18-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">否</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-18-1" tabindex="0"><span class="text">是</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                月度通知单
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.monthlyNote" placeholder="请选择月度通知单">
                                <option value="Y1">Y1:提供月度计租收入通知单－显示营业额</option>
                                <option value="Y9">Y9:提供月度计租收入通知单－显示营业额、应付抽成租金、应付固定租金、应付物业管理费</option>
                                <option value="Y0">Y0:提供月度计租收入通知单－允许手工修改</option>
                                <option value="Y">Y:提供月度计租收入通知单－显示营业额、应付抽成租金</option>
                                <option value="N">N:无需提供月度计租收入通知单</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-19" aria-haspopup="listbox" aria-expanded="false" title="N:无需提供月度计租收入通知单"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">N:无需提供月度计租收入通知单</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 1983.3px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-19" tabindex="-1" style="max-height: 1975.3px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-19-4"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-19-0" tabindex="0"><span class="text">Y1:提供月度计租收入通知单－显示营业额</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-19-1" tabindex="0"><span class="text">Y9:提供月度计租收入通知单－显示营业额、应付抽成租金、应付固定租金、应付物业管理费</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-19-2" tabindex="0"><span class="text">Y0:提供月度计租收入通知单－允许手工修改</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-19-3" tabindex="0"><span class="text">Y:提供月度计租收入通知单－显示营业额、应付抽成租金</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-19-4" tabindex="0" aria-setsize="5" aria-posinset="5" aria-selected="true"><span class="text">N:无需提供月度计租收入通知单</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                营业额取数规则
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.salesCalculation" placeholder="请选择营业额取数规则">
                                <option value="">请选择营业额取数规则</option>
                                <option value="C1">C1:主营业务收入-附加</option>
                                <option value="C2">C2:主营业务收入（有附加但不扣除）</option>
                                <option value="C3">C3:主营业务收入+其他业务收入-附加</option>
                                <option value="C4">C4:主营业务收入+其他业务收入（有附加但不扣除）</option>
                                <option value="GC1">GC1:主营业务收入-附加-外送费</option>
                                <option value="GT5">GT5:主营业务收入+其他业务收入+增值税-外送费</option>
                                <option value="GT7">GT7:主营业务收入+增值税-外送费</option>
                                <option value="L1">L1:按上月的主营业务收入-附加</option>
                                <option value="L2">L2:按上月的主营业务收入（有附加但不扣除）</option>
                                <option value="L3">L3:按上月的主营业务收入+其他业务收入-附加</option>
                                <option value="L4">L4:按上月的主营业务收入+其他业务收入（有附加但不扣除）</option>
                                <option value="R1">R1:转加盟店计租收入（承租人）</option>
                                <option value="R2">R2:转加盟店计租收入（出租人）</option>
                                <option value="R3">R3:Manual sales</option>
                                <option value="T1">T1:主营业务收入的堂食部分</option>
                                <option value="T5">T5:主营业务收入+其他业务收入+增值税</option>
                                <option value="T7">T7:主营业务收入+增值税</option>
                                <option value="W1">W1:主营业务收入的外送部分</option>
                                <option value="YC1">YC1:主营业务收入-附加-预包装收入</option>
                                <option value="YC5">YC5:主营业务收入+其他业务收入-预包装收入</option>
                                <option value="YT1">YT1:主营业务收入的堂食部分-预包装收入</option>
                                <option value="YT5">YT5:主营业务收入+其他业务收入+增值税-预包装收入</option>
                                <option value="YT7">YT7:主营业务收入+增值税-预包装收入</option>
                                <option value="YW1">YW1:主营业务收入的外送部分-预包装收入</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle bs-placeholder btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-20" aria-haspopup="listbox" aria-expanded="false" title="请选择营业额取数规则"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">请选择营业额取数规则</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2069.3px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-20" tabindex="-1" style="max-height: 2061.3px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-20-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-20-0" tabindex="0" aria-setsize="25" aria-posinset="1" aria-selected="true"><span class="text">请选择营业额取数规则</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-1" tabindex="0"><span class="text">C1:主营业务收入-附加</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-2" tabindex="0"><span class="text">C2:主营业务收入（有附加但不扣除）</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-3" tabindex="0"><span class="text">C3:主营业务收入+其他业务收入-附加</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-4" tabindex="0"><span class="text">C4:主营业务收入+其他业务收入（有附加但不扣除）</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-5" tabindex="0"><span class="text">GC1:主营业务收入-附加-外送费</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-6" tabindex="0"><span class="text">GT5:主营业务收入+其他业务收入+增值税-外送费</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-7" tabindex="0"><span class="text">GT7:主营业务收入+增值税-外送费</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-8" tabindex="0"><span class="text">L1:按上月的主营业务收入-附加</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-9" tabindex="0"><span class="text">L2:按上月的主营业务收入（有附加但不扣除）</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-10" tabindex="0"><span class="text">L3:按上月的主营业务收入+其他业务收入-附加</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-11" tabindex="0"><span class="text">L4:按上月的主营业务收入+其他业务收入（有附加但不扣除）</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-12" tabindex="0"><span class="text">R1:转加盟店计租收入（承租人）</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-13" tabindex="0"><span class="text">R2:转加盟店计租收入（出租人）</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-14" tabindex="0"><span class="text">R3:Manual sales</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-15" tabindex="0"><span class="text">T1:主营业务收入的堂食部分</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-16" tabindex="0"><span class="text">T5:主营业务收入+其他业务收入+增值税</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-17" tabindex="0"><span class="text">T7:主营业务收入+增值税</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-18" tabindex="0"><span class="text">W1:主营业务收入的外送部分</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-19" tabindex="0"><span class="text">YC1:主营业务收入-附加-预包装收入</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-20" tabindex="0"><span class="text">YC5:主营业务收入+其他业务收入-预包装收入</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-21" tabindex="0"><span class="text">YT1:主营业务收入的堂食部分-预包装收入</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-22" tabindex="0"><span class="text">YT5:主营业务收入+其他业务收入+增值税-预包装收入</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-23" tabindex="0"><span class="text">YT7:主营业务收入+增值税-预包装收入</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-20-24" tabindex="0"><span class="text">YW1:主营业务收入的外送部分-预包装收入</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                是否归还面积
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.lease.rentAreaYes" placeholder="请选择是否归还面积">
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-21" aria-haspopup="listbox" aria-expanded="false" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2155.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-21" tabindex="-1" style="max-height: 2147.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-21-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-21-0" tabindex="0"><span class="text">是</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-21-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">否</span></a></li></ul></div></div></div>
                        </div>
                    </div>
                </div>
                <!-- BR -->
                <div class="po-child open" id="LeaseDetail_br">
                    <div class="po-child-header display-flex-row align-items-center">
                        <label>付款信息</label>
                    </div>
                    <div class="po-child-content">
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                租赁年度定义
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.rb.rentAnnual" placeholder="请选择租赁年度定义">
                                <option value="滚动年度">滚动年度</option>
                                <option value="自然年度">自然年度</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-22" aria-haspopup="listbox" aria-expanded="false" title="滚动年度"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">滚动年度</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2291.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-22" tabindex="-1" style="max-height: 2283.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-22-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-22-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">滚动年度</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-22-1" tabindex="0"><span class="text">自然年度</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                付款方式
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.rb.paymentLease" placeholder="请选择付款方式">
                                <option value="N">N:按自然月度</option>
                                <option value="Y">Y:按租赁月度</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-23" aria-haspopup="listbox" aria-expanded="false" title="N:按自然月度"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">N:按自然月度</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2377.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-23" tabindex="-1" style="max-height: 2369.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-23-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-23-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">N:按自然月度</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-23-1" tabindex="0"><span class="text">Y:按租赁月度</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                付款期间与租金所属期间关系
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.rb.paymentRent" placeholder="请选择付款期间与租金所属期间关系" @change="if(currentLeaseDetail.rb.paymentRent=='0' || currentLeaseDetail.rb.paymentRent=='2'){ currentLeaseDetail.rb.paymentEarlyRentYesDay = ''} else if(currentLeaseDetail.rb.paymentRent=='1'){currentLeaseDetail.rb.paymentEarlyRentYesDay = '1'} else if(currentLeaseDetail.rb.paymentRent=='3'){currentLeaseDetail.rb.paymentEarlyRentYesDay = '2'}">
                                <option value="0">0:付款期间与租金所属期间一致：当月支付当月的租金</option>
                                <option value="1">1:付款期间早于租金所属期间</option>
                                <option value="2">2:付款期间晚于租金所属期间1个月</option>
                                <option value="3">3:付款期间晚于租金所属期间（超过1个月）</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-24" aria-haspopup="listbox" aria-expanded="false" title="0:付款期间与租金所属期间一致：当月支付当月的租金"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">0:付款期间与租金所属期间一致：当月支付当月的租金</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2463.3px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-24" tabindex="-1" style="max-height: 2455.3px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-24-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-24-0" tabindex="0" aria-setsize="4" aria-posinset="1" aria-selected="true"><span class="text">0:付款期间与租金所属期间一致：当月支付当月的租金</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-24-1" tabindex="0"><span class="text">1:付款期间早于租金所属期间</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-24-2" tabindex="0"><span class="text">2:付款期间晚于租金所属期间1个月</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-24-3" tabindex="0"><span class="text">3:付款期间晚于租金所属期间（超过1个月）</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                提前/晚于月份数
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <input type="text" maxlength="20" x-model="currentLeaseDetail.rb.paymentEarlyRentYesDay" class="sf-form-control w-form-control" placeholder="请输入提前/晚于月份数" data-init="{ alias: 'integer', suffix: ' 月', autoUnmask: true, min: 0, allowMinus: false, placeholder: '0', rightAlign: false }" x-bind:disabled="currentLeaseDetail.rb.paymentRent=='0' || currentLeaseDetail.rb.paymentRent=='2'" disabled="disabled" inputmode="numeric">
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                付款频率
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.rb.rbPaymentFrequency" placeholder="请选择付款频率" tabindex="null">
                                <option value="M">M:Monthly</option>
                                <option value="A">A:Quarterly1,4,7,10</option>
                                <option value="B">B:Quarterly2,5,8,11</option>
                                <option value="C">C:Quarterly3,6,9,12</option>
                                <option value="D">D:Semi Yearly1,7</option>
                                <option value="E">E:Semi Yearly2,8</option>
                                <option value="F">F:Semi Yearly3,9</option>
                                <option value="G">G:Semi Yearly4,10</option>
                                <option value="H">H:Semi Yearly5,11</option>
                                <option value="I">I:Semi Yearly6,12</option>
                                <option value="J">J:Odd Month</option>
                                <option value="K">K:Eve Month</option>
                                <option value="1">1:Annual - Jan</option>
                                <option value="2">2:Annual - Feb</option>
                                <option value="3">3:Annual - Mar</option>
                                <option value="4">4:Annual - Apr</option>
                                <option value="5">5:Annual - May</option>
                                <option value="6">6:Annual - Jun</option>
                                <option value="7">7:Annual - Jul</option>
                                <option value="8">8:Annual - Aug</option>
                                <option value="9">9:Annual - Sept</option>
                                <option value="X">X:Annual - Oct</option>
                                <option value="Y">Y:Annual - Nov</option>
                                <option value="Z">Z:Annual - Dec</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-25" aria-haspopup="listbox" aria-expanded="false" title="A:Quarterly1,4,7,10"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">A:Quarterly1,4,7,10</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 456.297px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-25" tabindex="-1" style="max-height: 448.297px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-25-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-25-0" tabindex="0"><span class="text">M:Monthly</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-25-1" tabindex="0" aria-setsize="24" aria-posinset="2" aria-selected="true"><span class="text">A:Quarterly1,4,7,10</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-2" tabindex="0"><span class="text">B:Quarterly2,5,8,11</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-3" tabindex="0"><span class="text">C:Quarterly3,6,9,12</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-4" tabindex="0"><span class="text">D:Semi Yearly1,7</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-5" tabindex="0"><span class="text">E:Semi Yearly2,8</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-6" tabindex="0"><span class="text">F:Semi Yearly3,9</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-7" tabindex="0"><span class="text">G:Semi Yearly4,10</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-8" tabindex="0"><span class="text">H:Semi Yearly5,11</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-9" tabindex="0"><span class="text">I:Semi Yearly6,12</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-10" tabindex="0"><span class="text">J:Odd Month</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-11" tabindex="0"><span class="text">K:Eve Month</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-12" tabindex="0"><span class="text">1:Annual - Jan</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-13" tabindex="0"><span class="text">2:Annual - Feb</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-14" tabindex="0"><span class="text">3:Annual - Mar</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-15" tabindex="0"><span class="text">4:Annual - Apr</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-16" tabindex="0"><span class="text">5:Annual - May</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-17" tabindex="0"><span class="text">6:Annual - Jun</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-18" tabindex="0"><span class="text">7:Annual - Jul</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-19" tabindex="0"><span class="text">8:Annual - Aug</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-20" tabindex="0"><span class="text">9:Annual - Sept</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-21" tabindex="0"><span class="text">X:Annual - Oct</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-22" tabindex="0"><span class="text">Y:Annual - Nov</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-25-23" tabindex="0"><span class="text">Z:Annual - Dec</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                付款类型
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.rb.rbPaymentType" placeholder="请选择付款类型">
                                <option value="N">N:先收发票后付款</option>
                                <option value="Y">Y:先付款后收发票</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-26" aria-haspopup="listbox" aria-expanded="false" title="N:先收发票后付款"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">N:先收发票后付款</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2721.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-26" tabindex="-1" style="max-height: 2713.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-26-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-26-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">N:先收发票后付款</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-26-1" tabindex="0"><span class="text">Y:先付款后收发票</span></a></li></ul></div></div></div>
                        </div>
                        <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '0' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                            <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <label class="form-label w-form-label">
                                    固定租金税率(%)
                                    <i class="error-icon"></i>
                                </label>
                                <select class="w-form-select" x-model="currentLeaseDetail.rb.rbtrRent" placeholder="请选择固定租金税率（%）">
                                    <option value="">0</option>
                                    <option value="I1ZJ">1</option>
                                    <option value="I3ZJ">3</option>
                                    <option value="I5ZJ">5</option>
                                    <option value="I6ZJ">6</option>
                                    <option value="I9ZJ">9</option>
                                    <option value="I13ZJ">13</option>
                                </select>
                            </div>
                        </template><div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <label class="form-label w-form-label">
                                    固定租金税率(%)
                                    <i class="error-icon"></i>
                                </label>
                                <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.rb.rbtrRent" placeholder="请选择固定租金税率（%）">
                                    <option value="">0</option>
                                    <option value="I1ZJ">1</option>
                                    <option value="I3ZJ">3</option>
                                    <option value="I5ZJ">5</option>
                                    <option value="I6ZJ">6</option>
                                    <option value="I9ZJ">9</option>
                                    <option value="I13ZJ">13</option>
                                </select><button type="button" tabindex="-1" class="btn dropdown-toggle bs-placeholder btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-13" aria-haspopup="listbox" aria-expanded="false" title="0"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">0</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2807.3px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-13" tabindex="-1" style="max-height: 2799.3px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-13-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-13-0" tabindex="0" aria-setsize="7" aria-posinset="1" aria-selected="true"><span class="text">0</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-13-1" tabindex="0"><span class="text">1</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-13-2" tabindex="0"><span class="text">3</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-13-3" tabindex="0"><span class="text">5</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-13-4" tabindex="0"><span class="text">6</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-13-5" tabindex="0"><span class="text">9</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-13-6" tabindex="0"><span class="text">13</span></a></li></ul></div></div></div>
                            </div>
                        <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '1' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                            <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <label class="form-label w-form-label">
                                    物管费税率（%）
                                    <i class="error-icon"></i>
                                </label>
                                <select class="w-form-select" x-model="currentLeaseDetail.rb.rbtrMgmt" placeholder="请选择物管费税率（%）">
                                    <option value="">0</option>
                                    <option value="I1ZJ">1</option>
                                    <option value="I3ZJ">3</option>
                                    <option value="I5ZJ">5</option>
                                    <option value="I6ZJ">6</option>
                                    <option value="I9ZJ">9</option>
                                    <option value="I13ZJ">13</option>

                                </select>
                            </div>
                        </template>
                        <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '2'">
                            <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">

                                <label class="form-label w-form-label">
                                    广告费税率（%）
                                    <i class="error-icon"></i>
                                </label>
                                <select class="w-form-select" x-model="currentLeaseDetail.rb.rbtrAdv" placeholder="请选择广告费税率（%）">
                                    <option value="">0</option>
                                    <option value="I1ZJ">1</option>
                                    <option value="I3ZJ">3</option>
                                    <option value="I5ZJ">5</option>
                                    <option value="I6ZJ">6</option>
                                    <option value="I9ZJ">9</option>
                                    <option value="I13ZJ">13</option>

                                </select>
                            </div>
                        </template>
                        <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                            <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <label class="form-label w-form-label">
                                    其他费用税率（%）
                                    <i class="error-icon"></i>
                                </label>
                                <select class="w-form-select" x-model="currentLeaseDetail.rb.rbtrAdv" placeholder="请选择其他费用税率（%）">
                                    <option value="">0</option>
                                    <option value="I1ZJ">1</option>
                                    <option value="I3ZJ">3</option>
                                    <option value="I5ZJ">5</option>
                                    <option value="I6ZJ">6</option>
                                    <option value="I9ZJ">9</option>
                                    <option value="I13ZJ">13</option>
                                </select>
                            </div>
                        </template>
                        <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                            <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <label class="form-label w-form-label">
                                    <b class="not_null">*</b>
                                    其他费用具体描述
                                    <i class="error-icon"></i>
                                </label>
                                <input type="text" maxlength="15" required="" x-model="currentLeaseDetail.rb.rbFeeDesc" class="sf-form-control w-form-control" placeholder="请输入其他费用具体描述">
                            </div>
                        </template>
                        <!-- phase -->
                        <div class="po-child-content-hasPhase">
                            <div class="po-child-header po-child-header-noline display-flex-row align-items-center">
                                <label>固定租金条款</label>
                                <a class="display-flex-row align-items-center add-phase m-l-auto" x-on:click="addRbPhase()">添加Phase</a>
                            </div>
                            <ul>
                                <template x-for="(phase ,phaseIndex) in currentLeaseDetail.rb.phases">
                                    <li x-init="initInputDateMask($el)" class="phase-child open">
                                        <div class="po-child-header po-child-header-noline display-flex-row align-items-center">
                                            <label x-text="'Phase ' + (currentLeaseDetail.rb.phases.indexOf(phase) + 1)"></label>
                                            <div class="m-l-auto right-p-btn">
                                                <a @click="deleteRbPhase(phaseIndex)">删除</a>
                                                <a class="put-right2" x-on:click="togglePhaseContent($event)">收起</a>
                                            </div>
                                        </div>
                                        <div class="po-child-content">
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>
                                                    开始日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="phase.rbBeginDate" x-bind:id="'rbBeginDate' + phaseIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择开始日期" x-bind:data-init="`{'dateTime':true,'endDateEl':'rbEndDate${phaseIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="">
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>结束日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="phase.rbEndDate" x-bind:id="'rbEndDate' + phaseIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择结束日期" x-bind:data-init="`{'dateTime':true,'startDateEl':'rbBeginDate${phaseIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="">
                                            </div>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '0' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input 
                                                        type="text" 
                                                        x-model="phase.rbAccruedAmountRent" 
                                                        class="sf-form-control w-form-control " 
                                                        placeholder="请输入固定租金计提" 
                                                        maxlength="100" 
                                                        x-bind:data-index="phaseIndex" 
                                                        data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" 
                                                        required=""
                                                    >
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '0' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" 
                                                            x-model="phase.rbPaymentAmountRent" 
                                                            x-bind:data-compute="`{
                                                                'sources': [
                                                                    'currentLeaseDetail.rb.rbPaymentFrequency',
                                                                    'phase.rbAccruedAmountRent'
                                                                ],
                                                                'calculate': 'calculateTotal'
                                                            }`" 
                                                            x-bind:data-index="phaseIndex" 
                                                            class="sf-form-control w-form-control " 
                                                            placeholder="请输入固定租金付款" 
                                                            maxlength="100" 
                                                            data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '1' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        物管费计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountMgmt" class="sf-form-control w-form-control " placeholder="请输入物管费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '1' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        物管费付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountMgmt" data-compute="{'sources': ['currentLeaseDetail.rb.rbPaymentFrequency','currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountMgmt'], 'calculate': 'calculateTotal'}" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入物管费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '2'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        广告费计提（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountAdv" class="sf-form-control w-form-control " placeholder="请输入广告费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '2'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        广告费付款（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountAdv" data-compute="{ 'sources': [ 'currentLeaseDetail.rb.rbPaymentFrequency', 'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountAdv' ], 'calculate': 'calculateTotal' }" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入广告费付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        其他费用计提（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountFee" class="sf-form-control w-form-control " placeholder="请输入其他费用计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        其他费用付款（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountFee" data-compute="{ 'sources': [ 'currentLeaseDetail.rb.rbPaymentFrequency', 'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountFee' ], 'calculate': 'calculateTotal' }" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入其他费用付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                        </div>
                                    </li>
                                </template><li x-init="initInputDateMask($el)" class="phase-child open">
                                        <div class="po-child-header po-child-header-noline display-flex-row align-items-center">
                                            <label x-text="'Phase ' + (currentLeaseDetail.rb.phases.indexOf(phase) + 1)">Phase 1</label>
                                            <div class="m-l-auto right-p-btn">
                                                <a @click="deleteRbPhase(phaseIndex)">删除</a>
                                                <a class="put-right2" x-on:click="togglePhaseContent($event)">收起</a>
                                            </div>
                                        </div>
                                        <div class="po-child-content">
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>
                                                    开始日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="phase.rbBeginDate" x-bind:id="'rbBeginDate' + phaseIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择开始日期" x-bind:data-init="`{'dateTime':true,'endDateEl':'rbEndDate${phaseIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="" id="rbBeginDate0" data-init="{'dateTime':true,'endDateEl':'rbEndDate0',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>结束日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="phase.rbEndDate" x-bind:id="'rbEndDate' + phaseIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择结束日期" x-bind:data-init="`{'dateTime':true,'startDateEl':'rbBeginDate${phaseIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="" id="rbEndDate0" data-init="{'dateTime':true,'startDateEl':'rbBeginDate0',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                                            </div>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '0' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountRent" class="sf-form-control w-form-control " placeholder="请输入固定租金计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template><div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountRent" class="sf-form-control w-form-control " placeholder="请输入固定租金计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="" inputmode="decimal">
                                                </div>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '0' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountRent" x-bind:data-compute="`{
                                                                'sources': [
                                                                    'currentLeaseDetail.rb.rbPaymentFrequency',
                                                                    'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountRent'
                                                                ],
                                                                'calculate': 'calculateTotal'
                                                            }`" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入固定租金付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template><div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountRent" x-bind:data-compute="`{
                                                                'sources': [
                                                                    'currentLeaseDetail.rb.rbPaymentFrequency',
                                                                    'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountRent'
                                                                ],
                                                                'calculate': 'calculateTotal'
                                                            }`" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入固定租金付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="" inputmode="decimal" data-compute="{
                                                                'sources': [
                                                                    'currentLeaseDetail.rb.rbPaymentFrequency',
                                                                    'currentLeaseDetail.rb.phases[0].rbAccruedAmountRent'
                                                                ],
                                                                'calculate': 'calculateTotal'
                                                            }" data-index="0">
                                                </div>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '1' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        物管费计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountMgmt" class="sf-form-control w-form-control " placeholder="请输入物管费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '1' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        物管费付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountMgmt" data-compute="{'sources': ['currentLeaseDetail.rb.rbPaymentFrequency','currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountMgmt'], 'calculate': 'calculateTotal'}" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入物管费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '2'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        广告费计提（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountAdv" class="sf-form-control w-form-control " placeholder="请输入广告费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '2'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        广告费付款（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountAdv" data-compute="{ 'sources': [ 'currentLeaseDetail.rb.rbPaymentFrequency', 'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountAdv' ], 'calculate': 'calculateTotal' }" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入广告费付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        其他费用计提（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountFee" class="sf-form-control w-form-control " placeholder="请输入其他费用计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        其他费用付款（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountFee" data-compute="{ 'sources': [ 'currentLeaseDetail.rb.rbPaymentFrequency', 'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountFee' ], 'calculate': 'calculateTotal' }" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入其他费用付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                        </div>
                                    </li><li x-init="initInputDateMask($el)" class="phase-child open">
                                        <div class="po-child-header po-child-header-noline display-flex-row align-items-center">
                                            <label x-text="'Phase ' + (currentLeaseDetail.rb.phases.indexOf(phase) + 1)">Phase 2</label>
                                            <div class="m-l-auto right-p-btn">
                                                <a @click="deleteRbPhase(phaseIndex)">删除</a>
                                                <a class="put-right2" x-on:click="togglePhaseContent($event)">收起</a>
                                            </div>
                                        </div>
                                        <div class="po-child-content">
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>
                                                    开始日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="phase.rbBeginDate" x-bind:id="'rbBeginDate' + phaseIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择开始日期" x-bind:data-init="`{'dateTime':true,'endDateEl':'rbEndDate${phaseIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="" id="rbBeginDate1" data-init="{'dateTime':true,'endDateEl':'rbEndDate1',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>结束日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="phase.rbEndDate" x-bind:id="'rbEndDate' + phaseIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择结束日期" x-bind:data-init="`{'dateTime':true,'startDateEl':'rbBeginDate${phaseIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="" id="rbEndDate1" data-init="{'dateTime':true,'startDateEl':'rbBeginDate1',regex:'/^\d{4}-\d{2}-\d{2}$/'}">
                                            </div>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '0' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountRent" class="sf-form-control w-form-control " placeholder="请输入固定租金计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template><div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountRent" class="sf-form-control w-form-control " placeholder="请输入固定租金计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="" inputmode="decimal">
                                                </div>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '0' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountRent" x-bind:data-compute="`{
                                                                'sources': [
                                                                    'currentLeaseDetail.rb.rbPaymentFrequency',
                                                                    'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountRent'
                                                                ],
                                                                'calculate': 'calculateTotal'
                                                            }`" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入固定租金付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template><div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        固定租金付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountRent" x-bind:data-compute="`{
                                                                'sources': [
                                                                    'currentLeaseDetail.rb.rbPaymentFrequency',
                                                                    'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountRent'
                                                                ],
                                                                'calculate': 'calculateTotal'
                                                            }`" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入固定租金付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="" inputmode="decimal" data-compute="{
                                                                'sources': [
                                                                    'currentLeaseDetail.rb.rbPaymentFrequency',
                                                                    'currentLeaseDetail.rb.phases[1].rbAccruedAmountRent'
                                                                ],
                                                                'calculate': 'calculateTotal'
                                                            }" data-index="1">
                                                </div>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '1' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        物管费计提（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountMgmt" class="sf-form-control w-form-control " placeholder="请输入物管费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '1' || currentLeaseDetail.lease.useOfLeaseNo == '4'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        物管费付款（元）
                                                        <i class="error-icon"></i>
                                                        <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountMgmt" data-compute="{'sources': ['currentLeaseDetail.rb.rbPaymentFrequency','currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountMgmt'], 'calculate': 'calculateTotal'}" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入物管费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '2'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        广告费计提（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountAdv" class="sf-form-control w-form-control " placeholder="请输入广告费计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '2'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        广告费付款（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountAdv" data-compute="{ 'sources': [ 'currentLeaseDetail.rb.rbPaymentFrequency', 'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountAdv' ], 'calculate': 'calculateTotal' }" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入广告费付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        其他费用计提（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbAccruedAmountFee" class="sf-form-control w-form-control " placeholder="请输入其他费用计提" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                            <template x-if="currentLeaseDetail.lease.useOfLeaseNo == '3'">
                                                <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                                    <label class="form-label w-form-label">
                                                        <b class="not_null">*</b>
                                                        其他费用付款（元）
                                                        <i class="error-icon"></i>
                                                    </label>
                                                    <input type="text" x-model="phase.rbPaymentAmountFee" data-compute="{ 'sources': [ 'currentLeaseDetail.rb.rbPaymentFrequency', 'currentLeaseDetail.rb.phases[${phaseIndex}].rbAccruedAmountFee' ], 'calculate': 'calculateTotal' }" x-bind:data-index="phaseIndex" class="sf-form-control w-form-control " placeholder="请输入其他费用付款" maxlength="100" data-init="{ alias: 'currency', groupSeparator: ',', radixPoint: '.', digits: 2, digitsOptional: false, autoUnmask: true, allowMinus: true, suffix: '元', rightAlign: false }" required="">
                                                </div>
                                            </template>
                                        </div>
                                    </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!-- SOV -->
                <div class="po-child open" id="LeaseDetail_sov">
                    <div class="po-child-header display-flex-row align-items-center">
                        <label>抽成租金</label>
                    </div>
                    <div class="po-child-content">
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                结算是否破月?
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.sov.sovBreakMonthYes" placeholder="请选择结算是否破月?">
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-27" aria-haspopup="listbox" aria-expanded="false" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 2993.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-27" tabindex="-1" style="max-height: 2985.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-27-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-27-0" tabindex="0"><span class="text">是</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-27-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">否</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                多档抽成计算方法
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.sov.calculationMethod" placeholder="请选择多档抽成计算方法">
                                <option value="S">S:跳档</option>
                                <option value="P">p:累进</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-28" aria-haspopup="listbox" aria-expanded="false" title="S:跳档"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">S:跳档</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 3079.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-28" tabindex="-1" style="max-height: 3071.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-28-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-28-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">S:跳档</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-28-1" tabindex="0"><span class="text">p:累进</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                付款类型
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.sov.sovPaymentType" placeholder="请选择付款类型">
                                <option value="N">N:先收发票后付款</option>
                                <option value="Y">Y:先付款后收发票</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-29" aria-haspopup="listbox" aria-expanded="false" title="N:先收发票后付款"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">N:先收发票后付款</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 3165.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-29" tabindex="-1" style="max-height: 3157.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-29-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-29-0" tabindex="0" aria-setsize="2" aria-posinset="1" aria-selected="true"><span class="text">N:先收发票后付款</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-29-1" tabindex="0"><span class="text">Y:先付款后收发票</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                增值税税率%
                                <i class="error-icon"></i>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.sov.sovLeaseVAT" placeholder="请选择增值税税率">
                                <option value="">0</option>
                                <option value="I1ZJ">1</option>
                                <option value="I3ZJ">3</option>
                                <option value="I5ZJ">5</option>
                                <option value="I6ZJ">6</option>
                                <option value="I9ZJ">9</option>
                                <option value="I13ZJ">13</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle bs-placeholder btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-30" aria-haspopup="listbox" aria-expanded="false" title="0"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">0</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 3251.3px; overflow: hidden; min-height: 128px;"><div class="inner open" role="listbox" id="bs-select-30" tabindex="-1" style="max-height: 3243.3px; overflow-y: auto; min-height: 120px;" aria-activedescendant="bs-select-30-0"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-30-0" tabindex="0" aria-setsize="7" aria-posinset="1" aria-selected="true"><span class="text">0</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-30-1" tabindex="0"><span class="text">1</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-30-2" tabindex="0"><span class="text">3</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-30-3" tabindex="0"><span class="text">5</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-30-4" tabindex="0"><span class="text">6</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-30-5" tabindex="0"><span class="text">9</span></a></li><li><a role="option" class="dropdown-item" id="bs-select-30-6" tabindex="0"><span class="text">13</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <label class="form-label w-form-label">
                                是否需要计算固定与抽成孰高
                                <i class="error-icon"></i>
                                <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="" data-original-title="AI取值定位"></a>
                            </label>
                            <div class="dropdown bootstrap-select w-form-select bs3 dropup"><select class="w-form-select" x-model="currentLeaseDetail.sov.minMaxNeedYes" placeholder="请选择是否需要计算固定与抽成孰高" @change="if($el.value=='Y'){currentLeaseDetail.sov.minMaxType1='RENP';currentLeaseDetail.sov.minMaxType2=''}else{currentLeaseDetail.sov.minMaxType1='';currentLeaseDetail.sov.minMaxType2=''}">
                                <option value="Y">是</option>
                                <option value="N">否</option>
                            </select><button type="button" tabindex="-1" class="btn dropdown-toggle btn-default" data-toggle="dropdown" role="combobox" aria-owns="bs-select-31" aria-haspopup="listbox" aria-expanded="false" title="否"><div class="filter-option"><div class="filter-option-inner"><div class="filter-option-inner-inner">否</div></div> </div><span class="bs-caret"><span class="caret"></span></span></button><div class="dropdown-menu open" style="max-height: 3337.3px; overflow: hidden; min-height: 0px;"><div class="inner open" role="listbox" id="bs-select-31" tabindex="-1" style="max-height: 3329.3px; overflow-y: auto; min-height: 0px;" aria-activedescendant="bs-select-31-1"><ul class="dropdown-menu inner " role="presentation" style="margin-top: 0px; margin-bottom: 0px;"><li><a role="option" class="dropdown-item" id="bs-select-31-0" tabindex="0"><span class="text">是</span></a></li><li class="selected active"><a role="option" class="dropdown-item active selected" id="bs-select-31-1" tabindex="0" aria-setsize="2" aria-posinset="2" aria-selected="true"><span class="text">否</span></a></li></ul></div></div></div>
                        </div>
                        <div class="w-public-input display-flex-column">
                            <input type="hidden" x-model="currentLeaseDetail.sov.minMaxType1">
                            <label class="checkbox-container">
                                <input type="checkbox" class="w-form-check-input" x-bind:disabled="currentLeaseDetail.sov.minMaxNeedYes=='N'" x-bind:checked="currentLeaseDetail.sov.minMaxType1=='RENP'" @change="if($el.checked==true){currentLeaseDetail.sov.minMaxType1='RENP'}else{currentLeaseDetail.sov.minMaxType1=''}" disabled="disabled">
                                <span class="checkbox-custom"></span>
                                <span class="checkbox-label">保底租金（和RENP取高）</span>
                            </label>
                        </div>
                        <template x-if="currentLeaseDetail.sov.minMaxNeedYes=='N'">
                            <div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <input type="hidden" x-model="currentLeaseDetail.sov.minMaxType2">
                                <label class="checkbox-container">
                                    <input type="checkbox" class="w-form-check-input" x-bind:checked="currentLeaseDetail.sov.minMaxType2=='MGMA'" @change="if($el.checked==true){currentLeaseDetail.sov.minMaxType2='MGMA'}else{currentLeaseDetail.sov.minMaxType2=''}">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-label">保底物管费（和MGMA取高）</span>
                                </label>
                            </div>
                        </template><div x-init="initInputDateMask($el)" class="w-public-input display-flex-column">
                                <input type="hidden" x-model="currentLeaseDetail.sov.minMaxType2">
                                <label class="checkbox-container">
                                    <input type="checkbox" class="w-form-check-input" x-bind:checked="currentLeaseDetail.sov.minMaxType2=='MGMA'" @change="if($el.checked==true){currentLeaseDetail.sov.minMaxType2='MGMA'}else{currentLeaseDetail.sov.minMaxType2=''}">
                                    <span class="checkbox-custom"></span>
                                    <span class="checkbox-label">保底物管费（和MGMA取高）</span>
                                </label>
                            </div>
                        <div class="po-child-content-hasPhase">
                            <div class="po-child-header po-child-header-noline display-flex-row align-items-center">
                                <label>变动条款明细</label>
                                <a class="display-flex-row align-items-center add-phase m-l-auto" x-on:click="addSovPhase()">添加Phase</a>
                            </div>
                            <ul>
                                <template x-for="(rule, ruleIndex) in currentLeaseDetail.sov.sovRules">
                                    <li class="phase-child open" x-init="initInputDateMask($el)">
                                        <div class="po-child-header po-child-header-noline display-flex-row align-items-center">
                                            <label x-text="'Phase ' + (ruleIndex + 1)"></label>
                                            <div class="m-l-auto right-p-btn">
                                                <a x-on:click="deleteSovPhase(ruleIndex)">删除</a>
                                                <a class="put-right2" x-on:click="togglePhaseContent($event)">收起</a>
                                            </div>
                                        </div>
                                        <div class="po-child-content">
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>
                                                    抽成开始日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="rule.sovBeginDate" autocomplete="off" x-bind:id="'sovBeginDate' + ruleIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择抽成开始日期" x-bind:data-init="`{'dateTime':true,'endDateEl':'sovEndDate${ruleIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="">
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>
                                                    抽成结束日期
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" x-model="rule.sovEndDate" autocomplete="off" x-bind:id="'sovEndDate' + ruleIndex" class="sf-form-control w-form-control w-form-control-date" placeholder="请选择抽成结束日期" x-bind:data-init="`{'dateTime':true,'startDateEl':'sovBeginDate${ruleIndex}',regex:'/^\\d{4}-\\d{2}-\\d{2}$/'}`" required="">
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    是否要基于抽成付款
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <select class="w-form-select" x-model="rule.paymentOnSovYes" placeholder="请选择是否要基于抽成付款">
                                                    <option value="Y">是</option>
                                                    <option value="N">否</option>
                                                </select>
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    是否需要结算
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <select class="w-form-select" x-model="rule.sovAnnualSettleYes" placeholder="请选择是否需要结算" @change="if(rule.sovAnnualSettleYes=='N') rule.annualSettleFrequency = '',rule.annualSettleMonth = '1'">
                                                    <option value="Y">是</option>
                                                    <option value="N">否</option>
                                                </select>
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    结算频率
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <select class="w-form-select" x-model="rule.annualSettleFrequency" placeholder="请选择结算频率">
                                                    <option value="">:年度租金结算</option>
                                                    <option value="S">S:半年度租金结算</option>
                                                    <option value="Q">Q:季度租金结算</option>
                                                    <option value="M">M:月度租金结算</option>
                                                </select>
                                            </div>
                                            <div class="w-public-input display-flex-column">
                                                <label class="form-label w-form-label">
                                                    <b class="not_null">*</b>
                                                    结算月份
                                                    <i class="error-icon"></i>
                                                    <a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a>
                                                </label>
                                                <input type="text" class="sf-form-control w-form-control " x-model="rule.annualSettleMonth" placeholder="请输入结算月份" data-init="{ alias: 'numeric', min: 1, max: 12, autoUnmask: true, allowMinus: false, suffix: ' 月', rightAlign: false }" required="">
                                            </div>
                                            <div class="po-child-header po-child-header-noline display-flex-row align-items-center">
                                                <label>比例及档位</label>

                                            </div>
                                            <div class="po-child-ratio">
                                                <table class="table offcanvas-body-table offcanvas-body-table-pub">
                                                    <thead>
                                                    <tr>
                                                        <th style="width:10%;" class="text-center">编号</th>
                                                        <th style="width:30%;">突破点金额/元<a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a></th>
                                                        <th style="width:24%;">付款抽成比例(%)<a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a></th>
                                                        <th style="width:24%;">计提抽成比例(%)<a class="aiPosition" data-toggle="tooltip" data-placement="top" title="AI取值定位"></a></th>
                                                        <th>操作</th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <template x-for="(ruleList, ruleListIndex) in rule.sovRuleList">
                                                        <tr x-init="initInputDateMask($el)">
                                                            <td class="text-center" x-text="ruleListIndex+1"></td>
                                                            <td>
                                                                <input type="text" class="sf-form-control w-form-control" autocomplete="off" x-model="ruleList.breakpoint" placeholder="请输入突破点金额（元）" maxlength="100" data-init="{ 'alias': 'currency', 'groupSeparator': ',', 'radixPoint': '.', 'digits': 2, 'digitsOptional': false, 'autoUnmask': true, 'allowMinus': false, 'suffix': '', 'rightAlign': false }">
                                                            </td>
                                                            <td>
                                                                    <input type="text" class="sf-form-control w-form-control" x-model="ruleList.sovPayPercentage" x-bind:disabled="ruleListIndex > 0" placeholder="请输入付款抽成比例%" data-init="{'placeholder': '', 'alias': 'decimal', 'digits': 6, 'digitsOptional': true, 'autoUnmask': true, 'rightAlign': false, 'allowMinus': false, 'suffix': ''}" autocomplete="off">
                                                            </td>
                                                            <td>
                                                                <input type="text" class="sf-form-control w-form-control" x-model="ruleList.sovAccPercentage" placeholder="请输入计提抽成比例%" autocomplete="off" maxlength="100" data-init="{'placeholder': '', 'alias': 'decimal', 'digits': 6, 'digitsOptional': true, 'autoUnmask': true, 'rightAlign': false, 'allowMinus': false, 'suffix': ''}">
                                                            </td>
                                                            <td>
                                                                <a class="qc" x-on:click="removeSovRuleList(rule,ruleListIndex)">删除</a>
                                                            </td>
                                                        </tr>
                                                    </template>
                                                    </tbody>
                                                </table>
                                                <div class="add-table-child">
                                                    <a x-on:click="addSovRuleList()" class="display-flex-row align-items-center">添加</a>
                                                </div>
                                            </div>
                                        </div>
                                    </li>
                                </template>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="saveResult display-flex-row align-items-center flex-end">
            <button class="popup-btn-border popup-btn-border2 m_r12" onclick="getOutData()">保存</button>
            <button class="popup-btn-border popup-btn-border-red m_r12">重新生成</button>
            <button class="popup-btn-red-gradient m_r12" onclick="validateAll()">生成报表</button>
        </div>
    </div>
</div>
<script type="module" src="/src/main.ts"></script>
<script>
    /**
     * data-compute 计算方法
     * @param params
     * @returns {*}
     */
     function calculateTotal(params) {
        var mRate = 12;
        var rbPaymentFrequency = params[0];
        if (rbPaymentFrequency == "M" || rbPaymentFrequency == "") {
            mRate = 1;
        } else if (rbPaymentFrequency == "J" || rbPaymentFrequency == "K") {
            mRate = 2;
        } else if (rbPaymentFrequency >= "A" && rbPaymentFrequency <= "C") {
            mRate = 3;
        } else if (rbPaymentFrequency >= "D" && rbPaymentFrequency <= "I") {
            mRate = 6;
        }

        var v = times(params[1], mRate);
        return v;
    }

    function times(num1, num2) {
        var others = [];
        for (var _i = 2; _i < arguments.length; _i++) {
            others[_i - 2] = arguments[_i];
        }
        if (others.length > 0) {
            return times.apply(void 0, [times(num1, num2), others[0]].concat(others.slice(1)));
        }
        var num1Changed = float2Fixed(num1);
        var num2Changed = float2Fixed(num2);
        var baseNum = digitLength(num1) + digitLength(num2);
        var leftValue = num1Changed * num2Changed;
        return leftValue / Math.pow(10, baseNum);
    }

    /**
     * Return digits length of a number
     * @param {*number} num Input number
     */
    function digitLength(num) {
        // Get digit length of e
        var eSplit = num.toString().split(/[eE]/);
        var len = (eSplit[0].split('.')[1] || '').length - (+(eSplit[1] || 0));
        return len > 0 ? len : 0;
    }
    /**
     * 把小数转成整数，支持科学计数法。如果是小数则放大成整数
     * @param {*number} num 输入数
     */
    function float2Fixed(num) {
        if (num.toString().indexOf('e') === -1) {
            return Number(num.toString().replace('.', ''));
        }
        var dLen = digitLength(num);
        return dLen > 0 ? strip(num * Math.pow(10, dLen)) : num;
    }
    document.addEventListener('DOMContentLoaded', () => {
        const testData = {
                "finance": {
                    "objectId": "8100238452072513536",
                    "deleted": false,
                    "createdAt": 1740044557910,
                    "updatedAt": 1740475740551,
                    "completedAt": 0,
                    "name": "佛山市三水区江帆大酒店有限公司.docx",
                    "status": 0,
                    "aiStatus": 2,
                    "contractId": "8121410958716174336",
                    "pushStatus": 0,
                    "creatorId": "1",
                    "creator": "管理员188",
                    "flowId": "",
                    "contId": "",
                    "relatedcont": false,
                    "contName": "佛山市三水区江帆大酒店有限公司.docx",
                    "contractFileId": "8121418565237473280",
                    "contPhotoFileId": "",
                    "aiCheck": true,
                    "desc": "",
                    "checkerId": "1",
                    "checkerName": "管理员188",
                    "firstCheckerId": "1",
                    "secondCheckerName": "管理员188",
                    "firstCheckerName": "管理员188",
                    "secondCheckerId": "1",
                    "currFlowId": "1",
                    "reviewComment": "",
                    "rejectTimes": 0,
                    "changeContType": "",
                    "buCode": "80054169",
                    "buName": "白坭KFC",
                    "finMarket": "广州财务部",
                    "contractType": "0",
                    "strategicAlliance": "1",
                    "bizType": "0",
                    "partyA": "佛山市三水区江帆大酒店有限公司",
                    "addOfPartyA": "",
                    "partyB": "百胜餐饮（广东）有限公司",
                    "addOfPartyB": "",
                    "addOfStore": "广东省佛山市佛山市三水区白坭镇工业大道85号",
                    "signDate": "",
                    "openDate": "",
                    "estimateOpen": "",
                    "returnAreaYes": "N",
                    "noSublet": "N",
                    "areaSM": "175",
                    "areaRemark": "1F(175)",
                    "certEffectiveDate": "",
                    "certExpiryDate": "",
                    "leaseTypePlanning": "P1",
                    "leaseTypeDE": "D2",
                    "leaseTypeRE": "R2",
                    "depositAmount": "",
                    "idcAmount": "",
                    "leaseIncentives": "",
                    "transferSubject": "N",
                    "reNotes": "",
                    "bondAmount": "78750.0",
                    "bondReturnDate": "2035-03-14",
                    "guaranteeAmount": "",
                    "relatedContId": "",
                    "leaseDetails": [
                        {
                            "objectId": "",
                            "lease": {
                                "objectId": "8121442745366478848",
                                "leaseCode": "5416901",
                                "useOfLeaseNo": "0",
                                "rentName": "123",
                                "rentType": "RS",
                                "landlordCode": "1234567",
                                "landlordName": "佛山市三水区江帆大酒店有限公司",
                                "receivedCode": "1234567",
                                "receivedName": "佛山市三水区江帆大酒店有限公司",
                                "taxplayerJde": "",
                                "taxplayerName": "",
                                "mergeOthersYes": "N",
                                "leaseGroup": "",
                                "strategyAlianceYes": "N",
                                "rentAreaYes": "N",
                                "yearlyNote": "N",
                                "receiverNote": "",
                                "monthlyNote": "N",
                                "unit": "1",
                                "beginDate": "2025-02-15",
                                "freeBeginDate": "2025-02-15",
                                "freeEndDate": "2025-03-15",
                                "startRentDate": "2025-03-16",
                                "orgEndDate": "2035-03-14",
                                "salesCalculation": "",
                                "relation": "{\"beginDate\":\"8121418546782535729\",\"freeEndDate\":\"8121418546782535732\",\"yearlyNote\":\"8121418546782535695\",\"monthlyNote\":\"8121418546782535696\",\"freeBeginDate\":\"8121418546782535731\",\"orgEndDate\":\"8121418546782535730\",\"receivedName\":\"8121418546782535725\",\"startRentDate\":\"8121418546782535733\",\"landlordName\":\"8121418546782535685\"}",
                                "deleted": false,
                                "createdAt": 1740360526504,
                                "updatedAt": 1740475740556,
                                "financeId": "8100238452072513536",
                                "anchor": {
                                    "objectId": null,
                                    "deleted": null,
                                    "createdAt": null,
                                    "updatedAt": null,
                                    "financeId": null,
                                    "leaseCode": null,
                                    "useOfLeaseNo": null,
                                    "rentName": null,
                                    "rentType": null,
                                    "landlordCode": null,
                                    "landlordName": "8121418546782535685",
                                    "receivedCode": null,
                                    "receivedName": "8121418546782535725",
                                    "taxplayerJde": null,
                                    "taxplayerName": null,
                                    "mergeOthersYes": null,
                                    "leaseGroup": null,
                                    "strategyAlianceYes": null,
                                    "rentAreaYes": null,
                                    "yearlyNote": "8121418546782535695",
                                    "receiverNote": null,
                                    "monthlyNote": "8121418546782535696",
                                    "unit": null,
                                    "beginDate": "8121418546782535729",
                                    "freeBeginDate": "8121418546782535731",
                                    "freeEndDate": "8121418546782535732",
                                    "startRentDate": "8121418546782535733",
                                    "orgEndDate": "8121418546782535730",
                                    "salesCalculation": null,
                                    "relation": null,
                                    "anchor": null,
                                    "calculateRou": null,
                                    "calculateType": null,
                                    "calculateDate": null
                                },
                                "calculateRou": "",
                                "calculateType": "",
                                "calculateDate": ""
                            },
                            "rb": {
                                "objectId": "8121442745366478848",
                                "rbPaymentFrequency": "M",
                                "rbPaymentType": "Y",
                                "paymentRent": "0",
                                "dba": "F0",
                                "paymentEarlyRentYesDay": "",
                                "paymentLease": "N",
                                "rbFeeDesc": "",
                                "rbAnnualSettleYes": "",
                                "rentAnnual": "滚动年度",
                                "rbtrRent": "I1ZJ",
                                "rbtrMgmt": "I1ZJ",
                                "rbtrAdv": "",
                                "rbtrFee": "",
                                "relation": "{\"paymentRent\":\"8121418546782535699\",\"rbPaymentType\":\"8121418546782535701\",\"paymentEarlyRentYesDay\":\"8121418546782535700\",\"rbPaymentFrequency\":\"8121418546782535702\",\"rentAnnual\":\"8121418546782535723\"}",
                                "phases": [
                                    {
                                        "objectId": "8121442745634914304",
                                        "deleted": false,
                                        "createdAt": 1740360526508,
                                        "updatedAt": 1740475740561,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2025-03-16",
                                        "rbEndDate": "2026-03-15",
                                        "rbAccruedAmountRent": "19000.0",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "19000",
                                        "rbPaymentAmountMgmt": "0",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442745769132032",
                                        "deleted": false,
                                        "createdAt": 1740360526510,
                                        "updatedAt": 1740475740563,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2026-03-16",
                                        "rbEndDate": "2027-03-15",
                                        "rbAccruedAmountRent": "19570.0",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "19570",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442745970458624",
                                        "deleted": false,
                                        "createdAt": 1740360526512,
                                        "updatedAt": 1740475740565,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2027-03-16",
                                        "rbEndDate": "2028-03-15",
                                        "rbAccruedAmountRent": "20157.1",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "20157.1",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442746037567488",
                                        "deleted": false,
                                        "createdAt": 1740360526514,
                                        "updatedAt": 1740475740567,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2028-03-16",
                                        "rbEndDate": "2029-03-15",
                                        "rbAccruedAmountRent": "20761.813333",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "20761.813333",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442746171785216",
                                        "deleted": false,
                                        "createdAt": 1740360526515,
                                        "updatedAt": 1740475740569,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2029-03-16",
                                        "rbEndDate": "2030-03-15",
                                        "rbAccruedAmountRent": "21384.6675",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "21384.6675",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442746238894080",
                                        "deleted": false,
                                        "createdAt": 1740360526517,
                                        "updatedAt": 1740475740570,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2030-03-16",
                                        "rbEndDate": "2031-03-15",
                                        "rbAccruedAmountRent": "22026.2075",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "22026.2075",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442746440220672",
                                        "deleted": false,
                                        "createdAt": 1740360526519,
                                        "updatedAt": 1740475740572,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2031-03-16",
                                        "rbEndDate": "2032-03-15",
                                        "rbAccruedAmountRent": "22686.993333",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "22686.993333",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442746708656128",
                                        "deleted": false,
                                        "createdAt": 1740360526524,
                                        "updatedAt": 1740475740574,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2032-03-16",
                                        "rbEndDate": "2033-03-15",
                                        "rbAccruedAmountRent": "23367.603333",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "23367.603333",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442746909982720",
                                        "deleted": false,
                                        "createdAt": 1740360526526,
                                        "updatedAt": 1740475740576,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2033-03-16",
                                        "rbEndDate": "2034-03-15",
                                        "rbAccruedAmountRent": "24068.631667",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "24068.631667",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    },
                                    {
                                        "objectId": "8121442747044200448",
                                        "deleted": false,
                                        "createdAt": 1740360526528,
                                        "updatedAt": 1740475740578,
                                        "rbId": "8121442745366478848",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2034-03-16",
                                        "rbEndDate": "2035-03-14",
                                        "rbAccruedAmountRent": "24790.690833",
                                        "rbAccruedAmountMgmt": "",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "24790.690833",
                                        "rbPaymentAmountMgmt": "",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbAccruedAmountRent\":\"8121418546782535704\",\"rbPaymentAmountRent\":\"8121418546782535704\",\"rbEndDate\":\"8121418546782535704\",\"rbBeginDate\":\"8121418546782535704\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535704",
                                            "rbEndDate": "8121418546782535704",
                                            "rbAccruedAmountRent": "8121418546782535704",
                                            "rbAccruedAmountMgmt": null,
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": "8121418546782535704",
                                            "rbPaymentAmountMgmt": null,
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    }
                                ],
                                "deleted": false,
                                "createdAt": 1740360526506,
                                "updatedAt": 1740475740558,
                                "anchor": {
                                    "objectId": null,
                                    "deleted": null,
                                    "createdAt": null,
                                    "updatedAt": null,
                                    "rbPaymentFrequency": "8121418546782535702",
                                    "rbPaymentType": "8121418546782535701",
                                    "paymentRent": "8121418546782535699",
                                    "dba": null,
                                    "paymentEarlyRentYesDay": "8121418546782535700",
                                    "paymentLease": null,
                                    "rbFeeDesc": null,
                                    "rbAnnualSettleYes": null,
                                    "rentAnnual": "8121418546782535723",
                                    "rbtrRent": null,
                                    "rbtrMgmt": null,
                                    "rbtrAdv": null,
                                    "rbtrFee": null,
                                    "relation": null,
                                    "anchor": null,
                                    "phases": null
                                }
                            },
                            "sov": {
                                "objectId": "8121442745366478848",
                                "sovBreakMonthYes": "Y",
                                "sovLeaseVAT": "I1ZJ",
                                "calculationMethod": "S",
                                "sovPaymentType": "N",
                                "minMaxNeedYes": "N",
                                "minMaxType1": "",
                                "minMaxType2": "",
                                "sovRules": [],
                                "deleted": false,
                                "createdAt": 1740360526531,
                                "updatedAt": 1740475740580,
                                "relation": "{\"calculationMethod\":\"8121418546782535706\",\"minMaxNeedYes\":\"8121418546782535703\",\"sovBreakMonthYes\":\"8121418546782535710\",\"sovPaymentType\":\"8121418546782535707\"}",
                                "anchor": {
                                    "objectId": null,
                                    "deleted": null,
                                    "createdAt": null,
                                    "updatedAt": null,
                                    "sovBreakMonthYes": "8121418546782535710",
                                    "sovLeaseVAT": null,
                                    "calculationMethod": "8121418546782535706",
                                    "sovPaymentType": "8121418546782535707",
                                    "minMaxNeedYes": "8121418546782535703",
                                    "minMaxType1": null,
                                    "minMaxType2": null,
                                    "relation": null,
                                    "anchor": null,
                                    "sovRules": null
                                }
                            }
                        },
                        {
                            "objectId": "",
                            "lease": {
                                "objectId": "8121440230998671360",
                                "leaseCode": "5416902",
                                "useOfLeaseNo": "1",
                                "rentName": "123",
                                "rentType": "RS",
                                "landlordCode": "12345678",
                                "landlordName": "佛山市三水区江帆大酒店有限公司",
                                "receivedCode": "12345678",
                                "receivedName": "佛山市三水区江帆大酒店有限公司",
                                "taxplayerJde": "",
                                "taxplayerName": "",
                                "mergeOthersYes": "N",
                                "leaseGroup": "",
                                "strategyAlianceYes": "N",
                                "rentAreaYes": "N",
                                "yearlyNote": "Y",
                                "receiverNote": "",
                                "monthlyNote": "Y1",
                                "unit": "2",
                                "beginDate": "2025-02-15",
                                "freeBeginDate": "2025-02-15",
                                "freeEndDate": "2025-03-15",
                                "startRentDate": "2025-03-16",
                                "orgEndDate": "2035-03-14",
                                "salesCalculation": "",
                                "relation": "{\"beginDate\":\"8121418546782535745\",\"freeEndDate\":\"8121418546782535748\",\"yearlyNote\":\"8121418546782535749\",\"monthlyNote\":\"8121418546782535750\",\"freeBeginDate\":\"8121418546782535747\",\"orgEndDate\":\"8121418546782535746\",\"receivedName\":\"8121418546782535744\",\"startRentDate\":\"8121418546782535739\",\"landlordName\":\"8121418546782535743\"}",
                                "deleted": false,
                                "createdAt": 1740360489037,
                                "updatedAt": 1740475740584,
                                "financeId": "8100238452072513536",
                                "anchor": {
                                    "objectId": null,
                                    "deleted": null,
                                    "createdAt": null,
                                    "updatedAt": null,
                                    "financeId": null,
                                    "leaseCode": null,
                                    "useOfLeaseNo": null,
                                    "rentName": null,
                                    "rentType": null,
                                    "landlordCode": null,
                                    "landlordName": "8121418546782535743",
                                    "receivedCode": null,
                                    "receivedName": "8121418546782535744",
                                    "taxplayerJde": null,
                                    "taxplayerName": null,
                                    "mergeOthersYes": null,
                                    "leaseGroup": null,
                                    "strategyAlianceYes": null,
                                    "rentAreaYes": null,
                                    "yearlyNote": "8121418546782535749",
                                    "receiverNote": null,
                                    "monthlyNote": "8121418546782535750",
                                    "unit": null,
                                    "beginDate": "8121418546782535745",
                                    "freeBeginDate": "8121418546782535747",
                                    "freeEndDate": "8121418546782535748",
                                    "startRentDate": "8121418546782535739",
                                    "orgEndDate": "8121418546782535746",
                                    "salesCalculation": null,
                                    "relation": null,
                                    "anchor": null,
                                    "calculateRou": null,
                                    "calculateType": null,
                                    "calculateDate": null
                                },
                                "calculateRou": "",
                                "calculateType": "",
                                "calculateDate": ""
                            },
                            "rb": {
                                "objectId": "8121440230998671360",
                                "rbPaymentFrequency": "M",
                                "rbPaymentType": "N",
                                "paymentRent": "0",
                                "dba": "F0",
                                "paymentEarlyRentYesDay": "",
                                "paymentLease": "N",
                                "rbFeeDesc": "",
                                "rbAnnualSettleYes": "",
                                "rentAnnual": "滚动年度",
                                "rbtrRent": "I1ZJ",
                                "rbtrMgmt": "I1ZJ",
                                "rbtrAdv": "",
                                "rbtrFee": "",
                                "relation": "{\"paymentRent\":\"8121418546782535755\",\"paymentEarlyRentYesDay\":\"8121418546782535756\",\"rbPaymentFrequency\":\"8121418546782535737\",\"rentAnnual\":\"8121418546782535753\"}",
                                "phases": [
                                    {
                                        "objectId": "8121440231334215680",
                                        "deleted": false,
                                        "createdAt": 1740360489042,
                                        "updatedAt": 1740475740589,
                                        "rbId": "8121440230998671360",
                                        "rbRateNum": 0,
                                        "rbBeginDate": "2025-03-16",
                                        "rbEndDate": "2035-03-14",
                                        "rbAccruedAmountRent": "",
                                        "rbAccruedAmountMgmt": "2222.00",
                                        "rbAccruedAmountAdv": "",
                                        "rbAccruedAmountFee": "",
                                        "rbPaymentAmountRent": "0",
                                        "rbPaymentAmountMgmt": "2222",
                                        "rbPaymentAmountAdv": "",
                                        "rbPaymentAmountFee": "",
                                        "relation": "{\"rbPaymentAmountMgmt\":\"8121418546782535751\",\"rbAccruedAmountMgmt\":\"8121418546782535752\",\"rbEndDate\":\"8121418546782535746\",\"rbBeginDate\":\"8121418546782535739\"}",
                                        "anchor": {
                                            "objectId": null,
                                            "deleted": null,
                                            "createdAt": null,
                                            "updatedAt": null,
                                            "rbId": null,
                                            "rbRateNum": null,
                                            "rbBeginDate": "8121418546782535739",
                                            "rbEndDate": "8121418546782535746",
                                            "rbAccruedAmountRent": null,
                                            "rbAccruedAmountMgmt": "8121418546782535752",
                                            "rbAccruedAmountAdv": null,
                                            "rbAccruedAmountFee": null,
                                            "rbPaymentAmountRent": null,
                                            "rbPaymentAmountMgmt": "8121418546782535751",
                                            "rbPaymentAmountAdv": null,
                                            "rbPaymentAmountFee": null,
                                            "relation": null,
                                            "anchor": null
                                        }
                                    }
                                ],
                                "deleted": false,
                                "createdAt": 1740360489039,
                                "updatedAt": 1740475740586,
                                "anchor": {
                                    "objectId": null,
                                    "deleted": null,
                                    "createdAt": null,
                                    "updatedAt": null,
                                    "rbPaymentFrequency": "8121418546782535737",
                                    "rbPaymentType": null,
                                    "paymentRent": "8121418546782535755",
                                    "dba": null,
                                    "paymentEarlyRentYesDay": "8121418546782535756",
                                    "paymentLease": null,
                                    "rbFeeDesc": null,
                                    "rbAnnualSettleYes": null,
                                    "rentAnnual": "8121418546782535753",
                                    "rbtrRent": null,
                                    "rbtrMgmt": null,
                                    "rbtrAdv": null,
                                    "rbtrFee": null,
                                    "relation": null,
                                    "anchor": null,
                                    "phases": null
                                }
                            },
                            "sov": {
                                "objectId": "8121440230998671360",
                                "sovBreakMonthYes": "Y",
                                "sovLeaseVAT": "I1ZJ",
                                "calculationMethod": "S",
                                "sovPaymentType": "Y",
                                "minMaxNeedYes": "N",
                                "minMaxType1": "",
                                "minMaxType2": "",
                                "sovRules": [],
                                "deleted": false,
                                "createdAt": 1740360489044,
                                "updatedAt": 1740475740590,
                                "relation": "{\"sovBreakMonthYes\":\"8121418546782535754\",\"sovPaymentType\":\"8121418546782535742\"}",
                                "anchor": {
                                    "objectId": null,
                                    "deleted": null,
                                    "createdAt": null,
                                    "updatedAt": null,
                                    "sovBreakMonthYes": "8121418546782535754",
                                    "sovLeaseVAT": null,
                                    "calculationMethod": null,
                                    "sovPaymentType": "8121418546782535742",
                                    "minMaxNeedYes": null,
                                    "minMaxType1": null,
                                    "minMaxType2": null,
                                    "relation": null,
                                    "anchor": null,
                                    "sovRules": null
                                }
                            }
                        }
                    ],
                    "nicknames": null,
                    "relation": "{\"depositAmount\":\"8121418546782535720\",\"leaseTypeDE\":\"8121418546782535694\",\"bondReturnDate\":\"8121418546782535716\",\"bondAmount\":\"8121418546782535715\",\"addOfPartyA\":\"8121418546782535684\",\"guaranteeAmount\":\"8121418546782535718\",\"addOfStore\":\"8121418546782535686,8121418546782535687,8121418546782535688\",\"addOfPartyB\":\"8121418546782535681\",\"idcAmount\":\"8121418546782535722\",\"partyA\":\"8121418546782535685\",\"partyB\":\"8121418546782535680\",\"areaSM\":\"8121418546782535689\",\"areaRemark\":\"8121418546782535692\",\"returnAreaYes\":\"8121418546782535728\"}",
                    "anchor": {
                        "objectId": null,
                        "deleted": null,
                        "createdAt": null,
                        "updatedAt": null,
                        "completedAt": null,
                        "name": null,
                        "status": null,
                        "aiStatus": null,
                        "contractId": null,
                        "pushStatus": null,
                        "creatorId": null,
                        "creator": null,
                        "flowId": null,
                        "contId": null,
                        "relatedcont": null,
                        "contName": null,
                        "contractFileId": null,
                        "contPhotoFileId": null,
                        "aiCheck": null,
                        "desc": null,
                        "checkerId": null,
                        "checkerName": null,
                        "firstCheckerId": null,
                        "secondCheckerName": null,
                        "firstCheckerName": null,
                        "secondCheckerId": null,
                        "currFlowId": null,
                        "reviewComment": null,
                        "rejectTimes": null,
                        "changeContType": null,
                        "buCode": null,
                        "buName": null,
                        "finMarket": null,
                        "contractType": null,
                        "strategicAlliance": null,
                        "bizType": null,
                        "partyA": "8121418546782535685",
                        "addOfPartyA": "8121418546782535684",
                        "partyB": "8121418546782535680",
                        "addOfPartyB": "8121418546782535681",
                        "addOfStore": "8121418546782535686,8121418546782535687,8121418546782535688",
                        "signDate": null,
                        "openDate": null,
                        "estimateOpen": null,
                        "returnAreaYes": "8121418546782535728",
                        "noSublet": null,
                        "areaSM": "8121418546782535689",
                        "areaRemark": "8121418546782535692",
                        "certEffectiveDate": null,
                        "certExpiryDate": null,
                        "leaseTypePlanning": null,
                        "leaseTypeDE": "8121418546782535694",
                        "leaseTypeRE": null,
                        "depositAmount": "8121418546782535720",
                        "idcAmount": "8121418546782535722",
                        "leaseIncentives": null,
                        "transferSubject": null,
                        "reNotes": null,
                        "bondAmount": "8121418546782535715",
                        "bondReturnDate": "8121418546782535716",
                        "guaranteeAmount": "8121418546782535718",
                        "relatedContId": null,
                        "leaseDetails": null,
                        "nicknames": null,
                        "relation": null,
                        "anchor": null,
                        "logs": null
                    },
                    "logs": null
                }
            };

        const FinancialSystemComponent=new FinancialSystemComponent(testData);

        function getOutData(){
            //const data = financialSystemComponent.outputData();
            //console.log('Output Data:', data);
            Alpine.store('financialSystemComponent').validateAll();
        }
    });
    function calculateTotal(values) {
        return Number(values[0]) * 100;
    }

    // 模拟异步数据获取函数
    async function fetchLeaseDetailData() {
        try {
            // 这里可以替换为实际的 API 调用
            // const response = await fetch('/api/lease-detail');
            // return await response.json();
            
            // 模拟异步请求延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 返回模拟数据
            return {
                objectId: Date.now().toString(),
                lease: {
                    objectId: Date.now().toString(),
                    leaseCode: 'AUTO_' + Date.now(),
                    useOfLeaseNo: '4',
                    rentName: '自动生成租金',
                    rentType: 'Fixed',
                    landlordCode: 'L001',
                    landlordName: '自动生成业主',
                    // ... 其他 lease 字段
                },
                rb: {
                    objectId: Date.now().toString(),
                    rbPaymentFrequency: 'Monthly',
                    rbPaymentType: 'Type 1',
                    phases: [],
                    // ... 其他 rb 字段
                },
                sov: {
                    objectId: Date.now().toString(),
                    sovRules: [],
                    // ... 其他 sov 字段
                }
            };
        } catch (error) {
            console.error('Error fetching lease detail data:', error);
            return null;
        }
    }

    // 异步验证函数 - 删除前确认
    async function validateLeaseDelete(objectId) {
        try {
            return new Promise((resolve) => {
                $.confirm({
                    title: '提示',
                    content: '您确认要删除该数据吗?',
                    confirmButton: '确定',
                    cancelButton: '取消',
                    confirm: function() {
                        let flag = true;
                        if (objectId) {
                            flag = false;
                            // 这里可以替换为实际的 API 调用
                            // 模拟 Ajax 调用
                            setTimeout(() => {
                                // 模拟调用成功
                                flag = true;
                                resolve(flag);
                            }, 500);
                            
                            // 实际的 Ajax 调用应该是这样：
                            /*
                            http.syncAjax('/manager/finance/deleteLease.htm', 
                                { objectId: objectId }, 
                                function(data) {
                                    flag = true;
                                    resolve(flag);
                                }
                            );
                            */
                        } else {
                            resolve(flag);
                        }
                    },
                    cancel: function() {
                        resolve(false);
                    }
                });
            });
        } catch (error) {
            console.error('Error in validateLeaseDelete:', error);
            return false;
        }
    }

    // 异步保存函数
    async function saveLeaseDetail(detail) {
        try {
            // 这里可以替换为实际的 API 调用
            // const response = await fetch('/api/lease-detail', {
            //     method: 'POST',
            //     headers: {
            //         'Content-Type': 'application/json',
            //     },
            //     body: JSON.stringify(detail)
            // });
            // return await response.json();
            
            // 模拟异步请求延迟
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 模拟保存成功
            console.log('Saving detail:', detail);
            return detail;
            
        } catch (error) {
            console.error('Error saving lease detail:', error);
            return null;
        }
    }
</script>
</body>
</html>
