{"name": "financial-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:dev": "tsc && vite build --mode development", "preview": "vite preview"}, "devDependencies": {"@rollup/plugin-inject": "^5.0.5", "@types/alpinejs": "^3.13.10", "@types/inputmask": "^5.0.7", "@vitejs/plugin-legacy": "^6.0.0", "typescript": "~5.6.2", "vite": "^6.0.5"}, "dependencies": {"@types/crypto-js": "^4.2.2", "alpinejs": "^3.14.3", "eventemitter3": "^5.0.1", "inputmask": "^5.0.9", "moment": "^2.30.1"}}